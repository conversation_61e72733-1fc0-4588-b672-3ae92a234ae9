const routes = [
  {
    path: "/*",
    wrappers: ["@/pages/wrapper"],
    component: "@/pages/error/404",
    // layout: false,
  },
  { 
    path: "/404", 
    exact: true,
    component: "@/pages/error/404",
    // layout: false
  },
  { 
    path: "/illegalPath", 
    exact: true,
    component: "@/pages/error/illegalPath",
    // layout: false
  },
  {
    path: "/noAuth",
    exact: true,
    component: "@/pages/error/noAuth",
    // layout: false,
  },
  {
    path: "/noNetwork",
    exact: true,
    component: "@/pages/error/noNetwork",
    // layout: false,
  },
  {
    path: "/master-common-iframe",
    exact: true,
    component: "@/pages/commonIframe/index",
  },
  { path: "/backend", wrappers: ["@/pages/wrapper"] },
  {
    path: "/homePage",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/homePage",
  },
  {
    path: "/myWorkbench",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/myWorkbench/work",
  },
  {
    path: "/myWorkbench/set",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/myWorkbench/set",
  },
  {
    path: "/productService",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/products",
  },
  {
    path: "/passService",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService",
  },
  {
    path: "/passService/componentDetail/:type/:id",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/componentDetail",
  },
  {
    path: "/passService/passAuthServerDetail/:type/:id/:chartName",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/passAuthServerDetail",
  },
  {
    path: "/passService/dataDesensitization/:type/:id/:chartName",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/dataDesensitization",
  },
  {
    path: "/passService/internationalization/:type/:id/:chartName",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/internationalization",
  },
  {
    path: "/passService/detail/:type/:id/:chartName",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/passSerDetail",
  },
  {
    path: "/passService/detail/apply/:type/:id/:chartName",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/passApply",
  },
  {
    path: "/passService/MessageComponentDetail/:id",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/messageDetail",
  },
  {
    path: "/apiMarket",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/apiMarket",
  },
  {
    path: "/apiMarket/detail/:id",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/apiMarket/detail",
  },
  {
    path: "/apiManage",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    // component: '@/pages/apiManage',
    routes: [
      // 子路由配置
      {
        path: "/apiManage/subscrip", // 订阅列表
        component: "@/pages/apiManage/apiManageComponent/MySubscriptions.jsx",
      },
      {
        path: "/apiManage/subscripLog", // 订阅日志
        component: "@/pages/apiManage/apiManageComponent/SubscripLog.jsx",
      },
      {
        path: "/apiManage/apiDebugger/:pid", // 订阅列表
        component: "@/pages/apiManage/apiManageComponent/ApiDebugger.jsx",
      },
      {
        path: "/apiManage/subscripLogDetail/:eid", // 订阅列表
        component: "@/pages/apiManage/apiManageComponent/SubscripLogDetail.jsx",
      },
    ],
  },
  {
    path: "/openSourceComponent",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    // component: "@/pages/openSourceComponent",
    // component: '@/pages/apiManage',
    routes: [
      // 子路由配置
      {
        path: "/openSourceComponent/list", // 基线管理
        component: "@/pages/openSourceComponent/Component/list.jsx",
      },
      {
        path: "/openSourceComponent/use", 
        component: "@/pages/openSourceComponent/Component/componentUse.jsx",
      },
      {
        path: "/openSourceComponent/review", 
        component: "@/pages/openSourceComponent/Component/reviewList.jsx",
      },
      {
        path: "/openSourceComponent/addReview/:taskId", 
        component: "@/pages/openSourceComponent/Component/addReviewList.jsx",
      }
    ],
  },
  // {
  //   path: "/openSourceComponent",
  //   wrappers: ["@/pages/wrapper"],
  //   exact: true,
  //   component: "@/pages/openSourceComponent",
  // },
  {
    path: "/festivalHolidayManage",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/festivalHoliday",
  },
  {
    path: "/holidayDetail",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    component: "@/pages/passService/holidayDetail",
  },
  {
    path: "/platformOperation",
    wrappers: ["@/pages/wrapper"],
    exact: true,
    // component: '@/pages/platformOperation',
    routes: [
      // 子路由配置
      {
        path: "/platformOperation/platformBoard", 
        component: "@/pages/platformOperation/platformBoard.jsx",
      },
      {
        path: "/platformOperation/middleplatformBoard", 
        component: "@/pages/platformOperation/middleplatformBoard.jsx",
      },
    ],
  },
{ path: '/fulltextSearchDetail', wrappers: ["@/pages/wrapper"], exact: true, component: '@/pages/passService/fulltextSearch' },
];

export default routes;
