# Gateway直接访问修改说明

## 修改概述

本次修改将所有原本通过nginx代理转发到gateway的请求改为前端直接请求gateway，不再经过nginx代理。

## 修改内容

### 1. 前端代码修改

#### 修改文件：`src/utils/axiosInstance.js`

**主要变更：**
- 添加了Gateway配置常量，包含不同环境的gateway地址
- 添加了环境检测函数 `getCurrentEnv()`
- 添加了需要直接请求gateway的路径配置 `GATEWAY_PATHS`
- 修改了所有HTTP请求方法（GET、POST、PUT、DELETE），在发送请求前检查是否需要直接请求gateway
- 如果请求路径匹配gateway路径，则自动拼接完整的gateway URL

**Gateway配置：**
```javascript
const GATEWAY_CONFIG = {
  dev: 'https://tasp-gateway-dev.trinasolar.com',
  test: 'https://tasp-gateway-test.trinasolar.com', 
  prod: 'https://tasp-gateway.trinasolar.com'
};
```

**直接请求gateway的路径：**
- `/kepler`
- `/harmony` 
- `/api`

### 2. Nginx配置修改

#### 修改的文件：
- `deploy/prod/nginx.conf` - 生产环境
- `deploy/test/nginx.conf` - 测试环境  
- `deploy/dev/nginx.conf` - 开发环境

**主要变更：**
注释掉了以下location块，因为前端现在直接请求gateway：

1. `location /kepler/` - kepler服务代理
2. `location /harmony/` - harmony服务代理
3. `location /api/doc/` - 知识库后端代理
4. `location /api/` - 通用API代理

## 工作原理

### 修改前的请求流程：
```
浏览器 → Nginx → Gateway → 后端服务
```

### 修改后的请求流程：
```
浏览器 → Gateway → 后端服务
```

## 环境检测逻辑

前端会根据当前访问的域名自动检测环境：
- 包含 `localhost` 或 `dev` → 开发环境
- 包含 `test` → 测试环境  
- 其他 → 生产环境

## 影响范围

### 受影响的API请求：
1. 所有以 `/kepler` 开头的请求
2. 所有以 `/harmony` 开头的请求
3. 所有以 `/api` 开头的请求

### 不受影响的功能：
1. 静态资源访问
2. 微前端应用代理（如 `/system/`, `/dochub/`, `/efficiency/` 等）
3. 中间件相关代理（如 `/apimiddleware/`）
4. 其他第三方服务代理

## 优势

1. **减少网络跳转**：请求直接到达gateway，减少了一层nginx代理
2. **简化架构**：减少了nginx配置的复杂性
3. **提高性能**：减少了请求延迟
4. **更好的错误处理**：前端可以直接处理gateway返回的错误信息

## 注意事项

1. **跨域问题**：需要确保gateway配置了正确的CORS策略
2. **SSL证书**：确保gateway的SSL证书有效
3. **网络策略**：确保客户端网络可以直接访问gateway地址
4. **监控调整**：需要调整相关的监控和日志收集策略

## 回滚方案

如果需要回滚到原来的代理方式：

1. 恢复nginx配置文件中被注释的location块
2. 在 `src/utils/axiosInstance.js` 中注释掉gateway直接访问的逻辑
3. 重新部署前端和nginx配置

## 测试建议

1. 测试所有API功能是否正常
2. 检查不同环境下的请求是否正确路由到对应的gateway
3. 验证错误处理是否正常
4. 检查网络性能是否有改善
