{"code": 0, "data": {"code": "api-tasp-test", "id": 799, "name": "API测试", "pubOrgId": null, "pubOrgName": "TestGroup", "tagId": 12, "tagName": "物联网", "version": "1.0.0", "apiSpec": "{\"info\":{\"title\":\"API测试\",\"description\":\"\",\"version\":\"1.0.0\",\"x-ibm-name\":\"api-tasp-test\"},\"tags\":[{\"name\":\"API TEST\"}],\"paths\":{\"/app-market-test/urlencoded\":{\"post\":{\"summary\":\"表单提交接口\",\"deprecated\":false,\"description\":\"通过POST请求传递form-urlencoded格式参数的示例接口\",\"operationId\":\"url\",\"tags\":[\"API TEST\"],\"parameters\":[{\"name\":\"username\",\"in\":\"query\",\"description\":\"用户名\",\"required\":true,\"type\":\"string\",\"x-example\":\"admin\"},{\"name\":\"password\",\"in\":\"query\",\"description\":\"密码\",\"required\":true,\"type\":\"string\",\"x-example\":\"password123\"}],\"responses\":{\"200\":{\"description\":\"表单提交成功\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}},\"400\":{\"description\":\"表单参数缺失或格式错误\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}}},\"produces\":[\"application/octet-stream\"]}},\"/app-market-test/upload\":{\"post\":{\"summary\":\"文件上传接口\",\"deprecated\":false,\"description\":\"通过POST请求上传文件的示例接口\",\"operationId\":\"handleUpload\",\"tags\":[\"API TEST\"],\"parameters\":[{\"name\":\"username\",\"in\":\"query\",\"description\":\"上传用户名\",\"required\":true,\"type\":\"string\",\"x-example\":\"testUser\"}],\"responses\":{\"200\":{\"description\":\"文件上传成功\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}},\"400\":{\"description\":\"文件为空或参数缺失\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}},\"500\":{\"description\":\"文件上传失败\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}}},\"consumes\":[\"multipart/form-data\"],\"produces\":[\"application/octet-stream\"]}},\"/app-market-test/api/body\":{\"post\":{\"summary\":\"处理JSON请求体数据\",\"deprecated\":false,\"description\":\"接收并验证TestBodyDTO类型的JSON请求体，返回包含相同数据的响应\",\"operationId\":\"handleRequestBody\",\"tags\":[\"API TEST\"],\"parameters\":[{\"name\":\"body\",\"in\":\"body\",\"schema\":{\"$ref\":\"#/definitions/TestBodyDTO\"}}],\"responses\":{\"200\":{\"description\":\"请求成功\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/TestBodyDTO\"}},\"400\":{\"description\":\"请求参数错误\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/TestBodyDTO\"}}},\"consumes\":[\"application/json\"],\"produces\":[\"application/octet-stream\"]}},\"/app-market-test/api/query\":{\"get\":{\"summary\":\"创建query参数接口\",\"deprecated\":false,\"description\":\"通过GET请求传递query参数的示例接口\",\"operationId\":\"query\",\"tags\":[\"API TEST\"],\"parameters\":[],\"responses\":{\"200\":{\"description\":\"成功返回字符串结果\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}},\"400\":{\"description\":\"请求参数错误\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}}},\"produces\":[\"application/octet-stream\"]}},\"/app-market-test/api/path/{id}\":{\"get\":{\"summary\":\"创建path参数接口\",\"deprecated\":false,\"description\":\"通过GET请求传递path参数的示例接口\",\"operationId\":\"path\",\"tags\":[\"API TEST\"],\"parameters\":[{\"name\":\"id\",\"in\":\"path\",\"description\":\"资源唯一标识ID\",\"required\":true,\"type\":\"string\",\"x-example\":\"123456\"}],\"responses\":{\"200\":{\"description\":\"成功返回路径参数值\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}},\"400\":{\"description\":\"路径参数格式错误\",\"headers\":{},\"schema\":{\"$ref\":\"#/definitions/CommonResult\"}}},\"produces\":[\"application/octet-stream\"]}}},\"security\":[{\"TSL-ClientID\":[],\"TSL-ClientSecret\":[]}],\"swagger\":\"2.0\",\"definitions\":{\"CommonResult\":{\"type\":\"object\",\"properties\":{\"code\":{\"type\":\"integer\",\"description\":\"状态码\",\"format\":\"int32\",\"example\":200},\"data\":{\"type\":\"object\",\"description\":\"业务数据\",\"properties\":{}},\"msg\":{\"type\":\"string\",\"description\":\"消息提示\",\"example\":\"操作成功\"},\"message\":{\"type\":\"string\",\"description\":\"消息提示\",\"example\":\"操作成功\"}},\"description\":\"通用响应结果\"},\"TestBodyDTO\":{\"required\":[\"name\"],\"type\":\"object\",\"properties\":{\"name\":{\"type\":\"string\",\"description\":\"名称\",\"example\":\"测试名称\"},\"description\":{\"type\":\"string\",\"description\":\"描述信息\",\"example\":\"这是一个测试DTO\"},\"test\":{\"$ref\":\"#/definitions/Test\"}},\"description\":\"测试请求体数据模型\"},\"Test\":{\"type\":\"object\",\"properties\":{\"age\":{\"type\":\"string\",\"description\":\"年龄\",\"example\":\"25\"},\"gender\":{\"type\":\"string\",\"description\":\"性别\",\"example\":\"男\"}},\"description\":\"测试嵌套对象模型\"}},\"securityDefinitions\":{\"TSL-ClientID\":{\"type\":\"apiKey\",\"in\":\"header\",\"name\":\"tsl-clientid\",\"x-key-type\":\"client_id\"},\"TSL-ClientSecret\":{\"type\":\"apiKey\",\"in\":\"header\",\"name\":\"tsl-clientsecret\",\"x-key-type\":\"client_secret\"}},\"x-components\":{},\"x-ibm-configuration\":{\"assembly\":{\"catch\":[{\"default\":[{\"gatewayscript\":{\"source\":\"var request = context.get('request');\\r\\nvar response = context.get('message');\\r\\nvar api = context.get('api');\\r\\nvar client = context.get('client');\\r\\n\\r\\nfixRequestBody(request, response, api, client, fixResponseBody, sendMessage);\\r\\n\\r\\nfunction fixRequestBody(request, response, api, client, callback1, callback2) {\\r\\n    if (request.body) {\\r\\n        context.request.body.readAsBuffer(function (errorObject, bufferObject) {\\r\\n            if (errorObject) {\\r\\n                callback1(request, response, api, client, callback2);\\r\\n            } else {\\r\\n                request.body = bufferObject.toString();\\r\\n                callback1(request, response, api, client, callback2);\\r\\n            }\\r\\n        });\\r\\n    } else {\\r\\n        callback1(request, response, api, client, callback2);\\r\\n    }\\r\\n}\\r\\n\\r\\n\\r\\nfunction fixResponseBody(request, response, api, client, callback) {\\r\\n    if (response.body) {\\r\\n        context.message.body.readAsBuffer(function (errorObject, bufferObject) {\\r\\n            if (errorObject) {\\r\\n                callback(request, response, api, client);\\r\\n            } else {\\r\\n                response.body = bufferObject.toString();\\r\\n                callback(request, response, api, client);\\r\\n            }\\r\\n        });\\r\\n    } else {\\r\\n        callback(request, response, api, client);\\r\\n    }\\r\\n}\\r\\n\\r\\n\\r\\nfunction sendMessage(request, response, api, client) {\\r\\n    var body = {request, response, api, client};\\r\\n    //发送邮件提醒\\r\\n    var urlopen = require('urlopen');\\r\\n    var options = {\\r\\n        target: 'http://10.50.163.235/callRecord/saveForDataPower',\\r\\n        method: 'post',\\r\\n        headers: {\\r\\n            'tsltoken': 'wtzjLzA2Tu4RcefgmvtVbxOS7wScqJ9w', 'Accept': 'application/json', 'Accept-Charset': 'utf-8'\\r\\n        },\\r\\n        contentType: 'application/json',\\r\\n        timeout: 60,\\r\\n        data: body\\r\\n    };\\r\\n    try {\\r\\n        urlopen.open(options, function (error, response) {\\r\\n            if (error) {\\r\\n                //如果邮件报错，将http code设置为500\\r\\n                context.message.statusCode = '500 ipaas-resend-api-error';\\r\\n            }\\r\\n        });\\r\\n    } catch (e) {\\r\\n        context.message.statusCode = '500 ipaas-resend-api-error';\\r\\n    }\\r\\n    //发送邮件告警结束\\r\\n}\\r\\n\",\"title\":\"gatewayscript\",\"version\":\"2.0.0\"}}]}],\"execute\":[{\"gatewayscript\":{\"source\":\"//© Copyright IBM Corp. Year 2020, 2021\\r\\nvar apim = require('apim')\\r\\nvar pathObj = apim.getvariable('request.path')\\r\\nvar remainPath = pathObj.substring(pathObj.indexOf('/',1),(pathObj.length))\\r\\nremainPath = remainPath.substring(remainPath.indexOf('/',1),(remainPath.length))\\r\\nvar searchObj = apim.getvariable('request.search')\\r\\napim.setvariable('remainPath',remainPath)\\r\\n\",\"title\":\"gatewayscript\",\"version\":\"2.0.0\"}},{\"invoke\":{\"version\":\"2.2.0\",\"title\":\"invoke\",\"backend-type\":\"detect\",\"header-control\":{\"type\":\"blocklist\",\"values\":[]},\"parameter-control\":{\"type\":\"blocklist\",\"values\":[]},\"timeout\":90,\"verb\":\"keep\",\"chunked-uploads\":false,\"persistent-connection\":true,\"cache-response\":\"protocol\",\"cache-ttl\":900,\"graphql-send-type\":\"detect\",\"http-version\":\"HTTP/1.1\",\"stop-on-error\":[],\"target-url\":\"$(target-url)$(remainPath)\",\"websocket-upgrade\":false}}]},\"enforced\":true,\"testable\":true,\"phase\":\"realized\",\"cors\":{\"enabled\":true},\"properties\":{\"target-url\":{\"description\":\"https://tasp-dev.trinasolar.com/ 环境\",\"encoded\":false,\"value\":\"https://tasp-dev.trinasolar.com/\"}},\"gateway\":\"datapower-api-gateway\",\"activity-log\":{\"enabled\":true,\"error-content\":\"payload\",\"success-content\":\"payload\"},\"buffering\":true,\"type\":\"rest\"},\"schemes\":[\"https\"],\"basePath\":\"/63046e094765ddb64a6a66fc/testgoup-catalog\",\"host\":\"https://se-outer-common-gw.apps.ocp4stg.trinasolar.com\"}", "gateway": ["https://apigw-stg.trinasolar.com", "https://se-inner-common-gw.apps.ocp4stg.trinasolar.com"], "sandboxClientId": "********************************", "sandboxClientSecret": "********************************", "apiEndpoints": [{"path": "/app-market-test/urlencoded", "fullPath": ["https://apigw-stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/urlencoded", "https://se-inner-common-gw.apps.ocp4stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/urlencoded"], "name": "表单提交接口", "desc": "通过POST请求传递form-urlencoded格式参数的示例接口", "httpMethod": "POST", "consumes": null, "parameters": [{"name": "username", "format": null, "in": "query", "type": "string", "description": "用户名", "enumValues": null, "required": true, "schemaRequired": null, "exampleValue": "", "defaultValue": null, "schema": null, "fields": null, "item": null, "reqBodyJson": null}, {"name": "password", "format": null, "in": "query", "type": "string", "description": "密码", "enumValues": null, "required": true, "schemaRequired": null, "exampleValue": "", "defaultValue": null, "schema": null, "fields": null, "item": null, "reqBodyJson": null}], "response": [{"statusCode": "200", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}, {"statusCode": "400", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}]}, {"path": "/app-market-test/upload", "fullPath": ["https://apigw-stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/upload", "https://se-inner-common-gw.apps.ocp4stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/upload"], "name": "文件上传接口", "desc": "通过POST请求上传文件的示例接口", "httpMethod": "POST", "consumes": "multipart/form-data", "parameters": [{"name": "username", "format": null, "in": "query", "type": "string", "description": "上传用户名", "enumValues": null, "required": true, "schemaRequired": null, "exampleValue": "", "defaultValue": null, "schema": null, "fields": null, "item": null, "reqBodyJson": null}], "response": [{"statusCode": "200", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}, {"statusCode": "400", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}, {"statusCode": "500", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}]}, {"path": "/app-market-test/api/body", "fullPath": ["https://apigw-stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/api/body", "https://se-inner-common-gw.apps.ocp4stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/api/body"], "name": "处理JSON请求体数据", "desc": "接收并验证TestBodyDTO类型的JSON请求体，返回包含相同数据的响应", "httpMethod": "POST", "consumes": "application/json", "parameters": [{"name": "body", "format": null, "in": "body", "type": "object", "description": "", "enumValues": null, "required": false, "schemaRequired": ["name"], "exampleValue": "", "defaultValue": null, "schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "example": "测试名称", "description": "名称"}, "description": {"type": "string", "example": "这是一个测试DTO", "description": "描述信息"}, "test": {"$ref": "#/definitions/Test"}}, "description": "测试请求体数据模型"}, "fields": [{"name": "name", "type": "string", "description": "名称", "required": true, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "description", "type": "string", "description": "描述信息", "required": false, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "test", "type": "object", "description": "测试嵌套对象模型", "required": false, "example": null, "defaultValue": null, "format": "", "item": null, "fields": [{"name": "age", "type": "string", "description": "年龄", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "gender", "type": "string", "description": "性别", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}]}], "item": null, "reqBodyJson": "{\"test\":{\"gender\":\"string\",\"age\":\"string\"},\"name\":\"string\",\"description\":\"string\"}"}], "response": [{"statusCode": "200", "description": null, "body": null, "fields": [{"name": "name", "type": "string", "description": "名称", "required": true, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "description", "type": "string", "description": "描述信息", "required": false, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "test", "type": "object", "description": "测试嵌套对象模型", "required": false, "example": null, "defaultValue": null, "format": "", "item": null, "fields": [{"name": "age", "type": "string", "description": "年龄", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "gender", "type": "string", "description": "性别", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}]}], "item": null, "respBodyJson": "{\"test\":{\"gender\":\"string\",\"age\":\"string\"},\"name\":\"string\",\"description\":\"string\"}"}, {"statusCode": "400", "description": null, "body": null, "fields": [{"name": "name", "type": "string", "description": "名称", "required": true, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "description", "type": "string", "description": "描述信息", "required": false, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "test", "type": "object", "description": "测试嵌套对象模型", "required": false, "example": null, "defaultValue": null, "format": "", "item": null, "fields": [{"name": "age", "type": "string", "description": "年龄", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "gender", "type": "string", "description": "性别", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}]}], "item": null, "respBodyJson": "{\"test\":{\"gender\":\"string\",\"age\":\"string\"},\"name\":\"string\",\"description\":\"string\"}"}]}, {"path": "/app-market-test/api/query", "fullPath": ["https://apigw-stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/api/query", "https://se-inner-common-gw.apps.ocp4stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/api/query"], "name": "创建query参数接口", "desc": "通过GET请求传递query参数的示例接口", "httpMethod": "GET", "consumes": null, "parameters": [], "response": [{"statusCode": "200", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}, {"statusCode": "400", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}]}, {"path": "/app-market-test/api/path/{id}", "fullPath": ["https://apigw-stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/api/path/{id}", "https://se-inner-common-gw.apps.ocp4stg.trinasolar.com/63046e094765ddb64a6a66fc/testgoup-catalog/app-market-test/api/path/{id}"], "name": "创建path参数接口", "desc": "通过GET请求传递path参数的示例接口", "httpMethod": "GET", "consumes": null, "parameters": [{"name": "id", "format": null, "in": "path", "type": "string", "description": "资源唯一标识ID", "enumValues": null, "required": true, "schemaRequired": null, "exampleValue": "", "defaultValue": null, "schema": null, "fields": null, "item": null, "reqBodyJson": null}], "response": [{"statusCode": "200", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}, {"statusCode": "400", "description": null, "body": null, "fields": [{"name": "code", "type": "integer", "description": "状态码", "required": null, "example": null, "defaultValue": null, "format": "int32", "item": null, "fields": null}, {"name": "data", "type": "object", "description": "业务数据", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": []}, {"name": "msg", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}, {"name": "message", "type": "string", "description": "消息提示", "required": null, "example": null, "defaultValue": null, "format": "", "item": null, "fields": null}], "item": null, "respBodyJson": "{\"msg\":\"string\",\"code\":0,\"data\":{},\"message\":\"string\"}"}]}]}, "msg": "", "message": null}