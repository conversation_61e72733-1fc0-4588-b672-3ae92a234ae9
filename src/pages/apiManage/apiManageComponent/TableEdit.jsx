import React, { useState, useEffect } from 'react';
import { Table, Input, Select, Button ,Upload} from 'antd';
import {
  PlusCircleOutlined,
  UploadOutlined, 
  DeleteOutlined
} from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
const EditableTable = ({dataSource,dataSourceOrigin,activeTab,handleDelete,setParamList,bodyType,handleChange,setSelectList,selectList,selectRowKey,setSelecteRowKey}) => {
  
  
 
  const [editingKey, setEditingKey] = useState('');
  const [editingCell, setEditingCell] = useState('');
  // const [selectedRowKeys, setSelectedRowKeys] = useState(['0', '1']);
  
 
  const inputRef=React.createRef();
  const columns = [
    {
      title: 'Key',
      dataIndex: 'name',
      render: (text, record) => {
        if (editingKey === record.key && editingCell === 'name') {
          return (
            <Input
              ref={inputRef}
              defaultValue={text}
              onBlur={handleSave}
              autoFocus
            />
          );
        }
        return (
          <div 
            onClick={() => handleCellClick(record.key, 'name')}
            style={{ padding: '5px', cursor: 'pointer',width:'100%' }}
          >
            {text || ''}
          </div>
        );
      },
    },
    {
      title: 'VALUE',
      dataIndex: 'exampleValue',
      render: (text, record) => {
        if (editingKey === record.key && editingCell === 'exampleValue') {
          return (
            <Input
              ref={inputRef}
              defaultValue={text}
              onBlur={handleSave}
              autoFocus
            />
          );
        }
        return (
          <div 
            onClick={() => handleCellClick(record.key, 'exampleValue')}
            style={{ padding: '5px', cursor: 'pointer',width:'100%' }}
          >
            {text || ''}
          </div>
        );
      },
    },
    {
      title: 'DESCRIPTION',
      dataIndex: 'description',
      render: (text, record) => {
        if (editingKey === record.key && editingCell === 'description') {
          return (
            <Input
              ref={inputRef}
              defaultValue={text}
           
              onBlur={handleSave}
              
            />
          );
        }
        return (
          <div 
            onClick={() => handleCellClick(record.key, 'description')}
            style={{ padding: '5px', cursor: 'pointer',width:'100%' }}
          >
            {text || ''}
          </div>
        );
      },
    },
    {
      title: '操作',
      render: (_, record,index) => (
        <Button type="link" danger onClick={() => handleDelete(record.key)}>
          删除
        </Button>
      ),
    },
  ];
  const columnsBody = [
    {
      title: 'Key',
      dataIndex: 'name',
      colSpan: 2,
      render: (text, record) => {
        if (editingKey === record.key && editingCell === 'name') {
          return (
            <Input
              ref={inputRef}
              defaultValue={text}
              onBlur={handleSave}
              autoFocus
            />
          );
        }
        return (
          <div 
            onClick={() => handleCellClick(record.key, 'name')}
            style={{ padding: '5px', cursor: 'pointer',width:'100%' }}
          >
            {text || ''}
          </div>
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'format',
      colSpan: 0,
      width: 100,
      render: (_, record) => (
        <Select
          value={record.format}
          onChange={(value) => handleChangeFile(record.key,value,'format')}
          style={{ width: '100%' }}
        >
          <Select.Option value="text">Text</Select.Option>
          <Select.Option value="binary">File</Select.Option>
        </Select>
      ),
    },
    {
      title: 'VALUE',
      dataIndex: 'exampleValue',
      render: (text, record) => {
        if (editingKey === record.key && editingCell === 'exampleValue') {
          if (record.format === 'binary') {
            return (
              <Upload
                beforeUpload={(file) => {
                  handleChangeFile(record.key,file, 'exampleValue');
                  return false; // 阻止自动上传
                }}
                showUploadList={false}
              >
                <Button icon={<UploadOutlined />}>
                  {record.file ? record.file.name : '选择文件'}
                </Button>
              </Upload>
            );
          }
          return (
            <Input
              ref={inputRef}
              defaultValue={text}
              onBlur={handleSave}
              autoFocus
            />
          );
        }
        if (record.format === 'binary') {
          return (
            <div 
              onClick={() => handleCellClick(record.key, 'exampleValue')}
              style={{ padding: '5px', cursor: 'pointer',width:'100%' }}
            >
              {text.name || ''}
            </div>
          );
        }
        return (
          <div 
            onClick={() => handleCellClick(record.key, 'exampleValue')}
            style={{ padding: '5px', cursor: 'pointer',width:'100%' }}
          >
            {text || ''}
          </div>
        );
      },
    },
    {
      title: 'DESCRIPTION',
      dataIndex: 'description',
      render: (text, record) => {
        if (editingKey === record.key && editingCell === 'description') {
          return (
            <Input
              ref={inputRef}
              defaultValue={text}
           
              onBlur={handleSave}
              
            />
          );
        }
        return (
          <div 
            onClick={() => handleCellClick(record.key, 'description')}
            style={{ padding: '5px', cursor: 'pointer',width:'100%' }}
          >
            {text || ''}
          </div>
        );
      },
    },
    {
      title: '操作',
      render: (_, record,index) => (
        <Button type="link" danger onClick={() => handleDelete(record.key)}>
          删除
        </Button>
      ),
    },
  ];
  const handleChangeFile = (key,value,type) => {
    let list = dataSourceOrigin[bodyType];
    const newData = [...list];
    const index = newData.findIndex(item => key === item.key);
    
    if (index > -1) {
      const item = newData[index];
      //当类型切换时重置值
      if (type === 'format' && value !== item.format) {
        item.exampleValue = '';
      }
      if(type === 'format'){
        newData[index] = { ...item, "format": value };
      }else{
        newData[index] = { ...item, "exampleValue": value };
      }
      
      dataSourceOrigin[bodyType]= [...newData];
      
      setParamList({...dataSourceOrigin})
       setEditingKey('');
    setEditingCell('');
    }
  };


  const handleCellClick = (key, type) => {
   
    setEditingKey(key);
    setEditingCell(type);
  };
// dataSourceOrigin?.bodyFormData:dataSourceOrigin?.bodyWww
//dataSourceOrigin[bodyType] = [...newData];
//处理
  const handleSave = (e) => {
    if(activeTab !== "body"){
      const newValue = e.target ? e.target.value : e;
      const newData = [...dataSource];
      const index = newData.findIndex(item => editingKey === item.key);
      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, { ...item, [editingCell]: newValue });
        dataSourceOrigin[activeTab] = [...newData];
        setParamList({...dataSourceOrigin})
      }
    }else{
      const newValue = e.target ? e.target.value : e;
      const newData = (bodyType === 'bodyFormData'? dataSourceOrigin?.bodyFormData:dataSourceOrigin?.bodyWww);
      const index = newData.findIndex(item => editingKey === item.key);
      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, { ...item, [editingCell]: newValue });
        dataSourceOrigin[bodyType] = [...newData];
        setParamList({...dataSourceOrigin})
      }
    }
    setEditingKey('');
    setEditingCell('');
  };

  const handleAdd = () => {
    const newKey = `new_${Date.now()}`;
    const newData = {
      name: '',
      exampleValue: '',
      description:'',
      format:'',
      key:newKey
    };
    if(activeTab !== 'body'){
      let dataSourceNew = dataSource || [];
      const newKey = `new_${Date.now()}`;
      dataSourceNew.push({
        name: '',
        exampleValue: '',
        description:'',
        format:'',
        key:newKey
      })
      dataSourceOrigin[activeTab] = [...dataSourceNew]
    }else{
      let list = [];
      let dataSource = (bodyType == 'bodyFormData'? dataSourceOrigin?.bodyFormData:dataSourceOrigin?.bodyWww);
      if(dataSource){
        list=dataSource
      }
      const a = ([...list, newData]);
      console.log(a)
      dataSourceOrigin[bodyType] = [...a]
    }
    setParamList({...dataSourceOrigin})
   
    // 自动聚焦到新行的第一个单元格
    setTimeout(() => {
      setEditingKey(newData.key);
      setEditingCell('name');
    }, 0);
  };

  const rowSelection = {
    selectedRowKeys:selectRowKey,
    onChange: (newSelectedRowKeys, selectedRows) => {
      setSelecteRowKey(newSelectedRowKeys)
      if(activeTab !== 'body'){
        switch (activeTab) {
          case "path":
            selectList.pathList = selectedRows;
            setSelectList({...selectList});
            break;
          case "query":
            selectList.queryList = selectedRows;
            setSelectList({...selectList});
            break;
          case "headers":
            selectList.headersList = selectedRows;
            setSelectList({...selectList});
            break;
        }
      }else{
        switch (bodyType) {
          case "bodyFormData":
            selectList.bodyFormDataList = selectedRows;
            setSelectList({...selectList});
            break;
          case "bodyWww":
            selectList.bodyWwwList = selectedRows;
            setSelectList({...selectList});
            break;
        }
      }
     
    },
    getCheckboxProps: (record) => ({
      disabled: record.name === 'tsl-clientid' || record.name === 'tsl-clientsecret',
      name: record.name
    }),
  };
 
 

  return (
    <div>
    {
      bodyType !== 'bodyRawParam'?<div style={{float:'right',color:'#1a9fe7',cursor:'pointer'}}  onClick={handleAdd} ><PlusCircleOutlined  style={{ marginBottom: 16 }} />新增</div>:''
    }
    {
      activeTab === 'body'?
      <div>
        {
          (bodyType === 'bodyFormData' || bodyType === 'bodyWww')?
          <Table
          bordered
          dataSource={bodyType=== 'bodyFormData'? dataSourceOrigin?.bodyFormData:dataSourceOrigin?.bodyWww}
          columns={bodyType=== 'bodyFormData'?columnsBody:columns}
          pagination={false}
          rowSelection={rowSelection}
          rowKey="key"
        />:<div>
          <CodeMirror
          value={dataSourceOrigin.bodyRawParam}
          height="200px"
          theme="light"
          extensions={[javascript(), yaml()]}
          onChange={handleChange}
        
          basicSetup={{
              lineNumbers: true,
              foldGutter: true,
              highlightActiveLine: true,
              dropCursor: true,
              allowMultipleSelections: true,
              indentOnInput: true,
              
          }}
          style={{ overflow: 'auto' }}
          />
        </div>
        }
      </div>:<>
        <Table
        bordered
        dataSource={dataSource}
        columns={columns}
        pagination={false}
        rowSelection={rowSelection}
        rowKey="key"
      />
      </>
    
    }
      
    </div>
  );
};

export default EditableTable;