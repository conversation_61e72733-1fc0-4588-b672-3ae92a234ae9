import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, CopyOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import InterfaceDoc from './detailBottom';
import './detail.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE, API_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Spin, notification ,Input,Typography,Table,message, Select } from 'antd';
import  apiLogoIMage from '@/assets/images/apiLogo.png';
import { subscribe,getApiDetail } from '@/services';
import _ from 'lodash'
import JsonView from '@uiw/react-json-view';
import { githubLightTheme } from '@uiw/react-json-view/githubLight';

// 开始
import Apply from './apply';
const { Search } = Input;
const apiDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 chartName 参数
  const { id } = location || {};
  const apiId = id;
  useEffect(() => {
   getApiDetailInfo();
  }, [])
  const operateData = (apiEndpoints) => {
    var index = 0
    apiEndpoints.forEach(apiEndpoint => {
      apiEndpoint.parameters?.forEach(param => {
        index ++;
        //设置一级目录的key
        param.key = index + ''
        if (param.in === 'body') {
            if(param.type === 'object'){
              param.children = param.fields
              parseFields(param.children, param.key);
            }else if(param.type === 'array'){
              parseitem(param,param.key)
            }
        }
      });
      apiEndpoint.response?.forEach(param => {
        index ++;
        //设置一级目录的key
        param.key = index + ''
        param.children = param.fields || []
        parseFields(param.children, param.key);
      });
    })
    // setBodyParam(last)
  }


const parseFields = (children, key) => {
  if (!children || !Array.isArray(children)) return;
  var index = 0
  children.forEach(field => {
    index ++
    //设置key
    field.key = key + '' + index
    if (field.type === 'object' && field.fields?.length > 0) {
      // 如果是对象类型且有fields，则创建嵌套对象
      field.children = field.fields
      parseFields(field.children, field.key);
    } else if(field.type === 'array'){
      parseitem(field, field.key);
    }
  });
}

const parseitem = (field, key) => {
  field.children = []
  field.children.push(field.item)
  parseFields(field.children, key);
}


  const handleBack = () => {
    history.back();
  };

 
  ///////开始//////////////
  const linkList = [
    {
      href: '/#/apiMarket',
      title: 'API市场'
    },
    {
      href: `/#/apiMarket/detail/${id}`,
      title: `API详情`
    },
  ]
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [detailInfo, setDetailInfo] = useState({});
  const [apiEndpoints, setApiEndpoints] = useState([]);
  const [activeLi, setActiveLi] = useState('');
  const [paramList, setParamList] = useState([]);
  const [bodyParam, setBodyParam] = useState([]);
  const [headerParam, setHeaderParam] = useState([]);
  const [queryParam, setQueryParam] = useState([]);
  const [pathParam, setPathParam] = useState([]);
  const [activeApi, setActiveApi] = useState({});
  const [apiEndpointsOrigin, setApiEndpointsOrigin] = useState([]);
  const [responseParam, setResponseParam] = useState([]);
  const [responseParamAll, setResponseParamAll] = useState({});
  const [currApiPath, setCurrApiPath] = useState('');
  
  const applyOPerate = () => {
    setIsModalOpen(true)
  }
  const handleCancel = () => {
    setIsModalOpen(false)
  }
  /////订阅/////
  const subscribeHandle =  (param) => {
    try {
        const dataParam = {
            ...param,
            productId:apiId
        }
        subscribe(dataParam).then((res) => {
     
          if(res){
            message.success("已提交API订阅申请，请前往审批中心-我的提交页面查看进度!")
            handleCancel()
          }else{
            message.error(res?.message || res?.msg)
          }
        }).catch((data) => {
      
          message.error(data.msg)
        })
    } catch (error) {
      console.log(error);
    }
  }
  ////////详情////////////
  const getApiDetailInfo =  () => {
    const param = {
      id:apiId
    }
    getApiDetail(param).then((res) => {
      if(res){
        console.log("我是详情接口数据", res)
        const apiEndpoints = res.apiEndpoints ?? [];
        setApiEndpointsOrigin(apiEndpoints)
        setApiEndpoints(apiEndpoints)
        operateData(apiEndpoints)
        changeActiveLi(apiEndpoints[0], apiEndpoints)
        setDetailInfo(res)
      }
    })
  }
  const processChildren =  (api,array) => {
    if(api.type === 'object'){
      array = [...array,...api.fields]
    }else if(api.type === 'array'){
      if(api.item?.type == "object"){
        array = [...array,...api.item.fields]
      }
    }else{
      array.push({
        name: api.name,
        description: api.description,
        type:api.type,
        required: api.required,
        exampleValue: api.exampleValue
      })
    }
    return array;
  }
  
  const changeActiveLi =  (item,listData) => {
    console.log(item)
    setActiveApi(item)
    setActiveLi(item?.path);
    // const apiEndpoints = listData || apiEndpoints;
    // const data = listData.filter((list)=>list.path === item.path);
    const paramList = item?.parameters || []
    const responseParamList = item?.response || []
    const headerParam =  paramList.filter((item)=>item['in'] === 'header')
    const queryParam =  paramList.filter((item)=>item['in'] === 'query')
    const pathParam =  paramList.filter((item)=>item['in'] === 'path')
    const bodyParam =  paramList.filter((item)=>item['in'] === 'body')
    setHeaderParam(headerParam)
    setQueryParam(queryParam)
    setPathParam(pathParam)
    setResponseParam(responseParamList[0]?.children)
    setCurrApiPath(item?.path ?? '')
    try{
      let ponseParamAll = JSON.parse(item?.response?.[0]?.respBodyJson) || {};
      setResponseParamAll(ponseParamAll)
    }catch(err){
      console.error('响应体JSON解析失败--', err)
      setResponseParamAll({})
    }
    if(bodyParam.length > 0){
     let array =  processChildren(bodyParam[0],[]);
     setBodyParam(array)
    }else{
      setBodyParam([])
    }
    
    setParamList(paramList)
  }
  const columns = [
    {
      title:  <span style={{ color: '#333', fontWeight: 'normal' }}>参数名称</span>,
      dataIndex: 'name',
      key: 'name',
      width: 250,
      ellipsis: true,
    },
    {
      title:  <span style={{ color: '#333', fontWeight: 'normal' }}>参数类型</span>,
      dataIndex: 'type',
      key: 'type',
      width: 200,
      ellipsis: true,
    },
    {
      title: <span style={{ color: '#333', fontWeight: 'normal' }}>是否必填</span>,
      dataIndex: 'required',
      key: 'required',
      width: 150,
      render: (text) => <span>{text?'必填':'非必填'}</span>,
      ellipsis: true,
    },
    {
      title:<span style={{ color: '#333', fontWeight: 'normal' }}>参数描述</span>,
      dataIndex: 'description',
     
      key: 'description',
      render: (text) => <div title={text} className='ellipsis-fix'>{text}</div>,
      ellipsis: true,
    },
  ];
  const onExpand=(expanded,record)=>{
    // console.log("aaa")
    // console.log(record)
    // if(record.type === 'object'){
    //   record.children.map((a)=>{
    //     if(a.type === 'array'){
    //         if(a.item === 'object'){
    //           a.children = a.item.fields
    //         }
    //     }
    //   })
    // }
    // console.log(record)
  }
  const copyText = async () => {
    let text = currApiPath
    if (!text) return;
    if (!navigator.clipboard) {
      // 兼容性处理
      try {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
        message.success('复制成功');
      } catch (err) {
        message.error('复制失败', err);
      }
      return;
    }
    try {
      await navigator.clipboard.writeText(text);
      message.success('复制成功');
    } catch (err) {
      message.error('复制失败', err);
    }
  };
  const onChange = _.debounce((e) => {
    let text = e.target.value?.toLowerCase();
    console.log(text)
    if(text){
      const list = apiEndpointsOrigin.filter((item)=> { return ((item.path.toLowerCase().indexOf(text) > -1) || (item.name.indexOf(e.target.value) > -1))});
      setApiEndpoints([...list])
    }else{
      setApiEndpoints([...apiEndpointsOrigin])
    }
  },300)
  const filterData = (value, children) => {
    if (!value) return children;
    if(children.children?.length > 0){
      filterData(children.children)
    }else{
      return children.filter(item => item.name.indexOf(value));
    }
    
  };
  
  const onTableSearch =  (value, _e, info, type) => {
    let dataSource = [];
    switch(type){
      case 'header':
        dataSource = headerParam
        break;
      case 'query':
        dataSource = queryParam
        break;
      case 'path':
        dataSource = pathParam
        break;
      case 'body':
        dataSource = bodyParam
        break;
      case 'response':
        dataSource = responseParam
        break;
      default:
        dataSource = [];
        break;
    }
    
  };

  return (
    <div className="middleware-detail sub-app-box-container">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} isNeedBack/>
      <div  className='micro-spin-container-box sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header">
          <div className="middleware-base-info-api">
            <div className='logo-box'>
              <img src={apiLogoIMage} className="middleware-logo-api" />
            </div>
            <div >
              <div className="logo-title">
                <div className="title-wrapper">
                  <div className="title">{detailInfo.name}</div>
                  <div className="subtitle">编码：{detailInfo.code}</div>
                  <div className="subtitle">版本：{detailInfo.version}</div>
                  <div className="subtitle">发布组：{detailInfo.pubOrgName}</div>
                </div>
              </div>
              <div className="action-buttons">
                <button className="action-btn primary" onClick={()=>{applyOPerate()}}>API订阅</button>
              </div>
            </div>

          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section">
          <div className="info-section-box">
            <div className="info-section-left">
              <h3 className="section-title">基本信息</h3>
              <div>
                <Search placeholder="根据路径或者名称进行搜索" onChange={(e) => onChange(e)} allowClear/>
                <div>
                  <ul className="info-section-ul">
                  {
                    apiEndpoints.map((item)=>  <li className={activeLi === item.path ? 'activeLi cursor-pointer':'normalLi cursor-pointer'} onClick={()=>{changeActiveLi(item,apiEndpoints)}} >
                      <p className='ellipsis'>{item.name}</p>
                      <p className='ellipsis'>{item.path}</p>
                    </li>)
                  }
                  
                  </ul>
                </div>
              </div>
            </div>
            <div className="info-section-right">
              <div>
              
                <div className="info-section-right-des">
                  <p>接口名称<span style={{padding:'0px 20px'}}>{activeApi?.name}</span></p>
                  <div style={{color:'#8a8a8a'}}>接口地址
                    <span style={{color:'#008BD6',padding:'0 20px'}}>{currApiPath}</span>
                    {currApiPath ? <span style={{marginLeft:'10px'}}> <CopyOutlined className="copy-icon hover-link" onClick={copyText}/></span>:''}
                  </div>
                
                  <p>接口描述<span style={{padding:'0px 20px'}}>{activeApi?.desc}</span></p>
                  <p>请求方式<a style={{padding:'0px 20px',color:'#008BD6'}}>{activeApi?.httpMethod}</a></p>
                </div>
              
                <div className="info-section-right-title-noBorder">请求参数</div>
                {
                  pathParam.length > 0?
                  <div className='param-box'>
                      <div className="info-section-right-title-noBorder info-section-right-title-small">Path参数</div>
                      <Table
                      columns={columns}
                      dataSource={pathParam}
                      onExpand={(expanded, record)=>{onExpand(expanded, record)}}
                    />
                  </div>:''
                }
                {
                  queryParam.length > 0 &&   <div className='param-box'>
                  <div className="info-section-right-title-noBorder info-section-right-title-small">Query参数</div>
                  <Table
                    columns={columns}
                    dataSource={queryParam}
                    onExpand={(expanded, record)=>{onExpand(expanded, record)}}
                  />
                </div>
                }
                {
                  headerParam.length > 0 &&  <div className='param-box'>
                    <div className="info-section-right-title-noBorder info-section-right-title-small">Header参数</div>
                    <Table
                      columns={columns}
                      dataSource={headerParam}
                      onExpand={(expanded, record)=>{onExpand(expanded, record)}}
                    />
                  </div>
                }
                {
                  bodyParam.length > 0 &&  
                  <div className='param-box'>
                    <div className="info-section-right-title-noBorder info-section-right-title-small info-section-right-flex">
                      <span>Body参数</span>
                      <Search placeholder="请输入参数名称" 
                      // onSearch={(...args) => onTableSearch(...args,'body')} 
                      style={{ width: 200 }} />
                    </div>
                    <Table
                      columns={columns}
                      dataSource={bodyParam}
                      onExpand={(expanded, record)=>{onExpand(expanded, record)}}
                    />
                  </div>
                }
                <div className="info-section-right-title-noBorder">响应参数</div>
                {
                  responseParam?.length > 0 &&  <div className='param-box'>
                    <div className="info-section-right-title-noBorder info-section-right-title-small">响应体</div>
                    <Table
                    columns={columns}
                    dataSource={responseParam}
                    onExpand={(expanded, record)=>{onExpand(expanded, record)}}
                  />
                  </div>
                }
               
                {
                  responseParam?.length > 0 &&  <div>
                    <div className="info-section-right-title-small">预览</div>
                    <div style={{marginTop:"32px", maxHeight:'600px',overflow:'auto'}}>
                      <JsonView value={responseParamAll} displayDataTypes={false} style={githubLightTheme}/>
                    </div>
                  </div>
                }
               
             
              </div>
            </div>
          </div>
        </div>
      </div>
     
      
        {
          isModalOpen?<Apply isModalOpen={isModalOpen} handleCancel={handleCancel} subscribeHandle={subscribeHandle} info={detailInfo}/>:''
        }
    
    </div>
  );
};

export default apiDetail; 