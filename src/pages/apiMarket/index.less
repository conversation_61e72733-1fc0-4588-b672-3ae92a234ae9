.products-wrapper {
    display: flex;
    align-items: stretch;
    min-height: 600px;
    padding-top: 0 !important;
    gap: 24px;
    .group-name {
      font-weight: 500;
      font-size: 20px;
      line-height: 32px;
      margin-bottom: 8px;
      font-size: 16px;
    }
    .title-left {
        font-size: 14px;
        font-weight: 500;
        line-height: 32px;
        position: relative;
        padding-left: 16px;
        white-space: nowrap;
        letter-spacing: 1px;
    }
    .title-left::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 6px;
        height: 1.1em;
        border-radius: 4px;
        content: '';
        transform: translateY(-50%);
        background: #008cd6;
    }
  
    .search {
      border-bottom: 1px solid #e6e6e6;
  
      button {
        box-sizing: content-box;
        border-top-color: transparent;
        border-radius: 0;
        border-left: none;
        border-right: none;
        background: transparent;
        border-bottom-color: transparent;
      }
  
      .@{ant-prefix}-input-group-addon {
        background: transparent;
      }
  
      .@{ant-prefix}-input-affix-wrapper {
        border-right: none;
        // border-bottom: 1px solid rgb(217, 217, 217);
        border-radius: 0;
  
        &.@{ant-prefix}-input-affix-wrapper-focused {
          border-bottom-color: @primary-color;
          & + span button {
            border-bottom-color: @primary-color;
          }
        }
      }
    }
  
    .left-sidebar {
      flex: 240px 0 0;
      background: #fff;
      padding-top: 24px;
      line-height: 32px;
  
      // .item-padding {
      //   padding: 0 24px;
      // }
      .nav-filter {
        font-size: 16px;
      }
      .nav-btn {
        padding-left: 0;
        text-align: left;
        margin-bottom: 12px;
      }
      .nav-list {
        width: 100%;
        padding: 0 20px;
      }
      .nav-item {
        cursor: pointer;
        line-height: 36px;
        display: flex;
        column-gap: 4px;
        margin: 0;
        padding-left: 16px;
        &:hover {
          background: #e6f3fb;
        }
      }
  
      .search-bar {
        padding:  0 24px 16px;
      }
    }
    .main-content {
      flex: 1 0 0;
      width: 0;
  
      .search-bar {
        padding-top: 24px;
        padding-bottom: 16px;
        background: #fff;
  
        .search-tip {
          padding-left: 8px;
          padding-top: 8px;
          color: @text-color-secondary;
        }
      }
  
      .hide-search-bar {
        padding: 0;
      }
  
      .no-search-bar {
        padding-top: 24px;
      }
  
      .prod-list {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
  
        padding-bottom: 48px;
        grid-gap: 20px;
  
        @media screen and (max-width: 992px) {
          grid-template-columns: 1fr 1fr;
        }
        @media screen and (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }
      .prod-card {
        background: #fff;
        border-radius: 8px;
        border: 1px solid #e6e6e6;
        transition: all 0.3s ease;
        padding: 20px;
  
        line-height: 24px;
  
        &-title {
          font-size: 16px;
          font-weight: 500;
        }
  
        &-desc {
          padding-top: 12px;
          color: #767676;
        }
  
        // &:hover {
        //   transform: translateY(-2px);
        //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        // }
  
        &.prod-card-link:hover {
          cursor: pointer;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
      }
    }
    .menu-box{
      .micro-menu-item{
        border-radius: 0px ;
      }
    }
  }
  
  
  
  .pass-service,
  .product-service {
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
  
    .content-wrapper {
      width: 100%;
      margin: 0 auto;
    }
  
    .header-content {
      padding-top: 5%;
      margin-top: 0;
    
    }
  
    .home-header {
      text-align: left;
      height: 400px;
      width: 100%;
      background-image: url('~@/assets/images/header-prod.svg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
  
      h1 {
        font-size: 32px;
        margin-bottom: 16px;
        font-weight: 500;
        color: #000;
      }
  
      .description {
        color: #666;
        font-size: 14px;
        line-height: 1.8;
        width: 50%;
      }
    }
  
    // 响应式调整
    @media screen and (min-width: 1920px) {
      .dealPadding {
        padding: 24px calc((100vw - 81%) / 2);
      }
    }
  
    // 响应式调整
    @media screen and (max-width: 1920px) {
      .dealPadding {
        padding: 24px 120px;
      }
    }
  
    // 响应式调整
    @media screen and (max-width: 1800px) {
      .dealPadding {
        padding: 24px calc((100vw - 81%) / 2);
      }
    }
     // 响应式调整
     @media screen and (max-width: 1700px) {
      .dealPadding {
        padding: 24px calc((100vw - 86%) / 2);
      }
    }
  
    @media screen and (max-width: 1600px) {
      .dealPadding {
        padding: 24px calc((100vw - 1200px) / 2);
      }
    }
  
    @media screen and (max-width: 1200px) {
      .dealPadding {
        padding: 24px 40px;
      }
    }
  
    @media screen and (max-width: 768px) {
      .dealPadding {
        padding: 24px 20px;
      }
  
      .home-header {
        padding: 24px;
  
        h1 {
          font-size: 24px;
        }
  
        .description {
          font-size: 14px;
        }
      }
    }
  
    .statistics-card-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      margin: 20px 0;
      position: relative;
      height: 24px;
    }
  
    .feature-cards {
      display: flex;
      justify-content: space-around;
      align-items: center;
      position: absolute;
      top: -70px;
      background-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    //   backdrop-filter: blur(5px);
      cursor: pointer;
  
      // 响应式调整
      @media screen and (min-width: 1921px) {
        width: 81%;
      }
  
      // 响应式调整
      @media screen and (max-width: 1920px) {
        width: 87.5%;
      }
      // 响应式调整
      @media screen and (max-width: 1800px) {
        width: 81%;
      }
      // 响应式调整
      @media screen and (max-width: 1700px) {
        width: 86%;
      }
      // 响应式调整
      @media screen and (max-width: 1500px) {
        width: 83%;
      }
      @media screen and (max-width: 1300px) {
        width: 94%;
      }
    }
  
    .feature-card {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      text-align: left;
      padding: 20px;
      height: 100px;
  
      &.active {
  
        h3 {
          color: #1890ff;
          font-weight: 600;
        }
  
        .feature-icon {
          transform: scale(1.1);
          transition: transform 0.3s ease;
        }
      }
  
      &:hover {
        transform: translateY(-5px);
        // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  
    .feature-icon {
      width: 60px;
      height: 60px;
      margin-right: 20px;
    }
  
    .feature-text {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
  
      h3 {
        transition: color 0.3s ease;
      }
    }
  
    .feature-card p {
      margin: 0;
      line-height: 1.5;
      white-space: normal;
    }
  }  