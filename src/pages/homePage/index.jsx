import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography, Statistic } from 'antd';
import Footer from './Footer';
import './index.less';
import tubiaoImage from '../../assets/images/desknew.svg';
import mapImage from '../../assets/images/map.png';
import api from '../../assets/images/api.svg';
import fuwu from '../../assets/images/fuwu.svg';
import wendang from '../../assets/images/wendang.svg';
import yingyong from '../../assets/images/yingyong.svg';
import zhongjianjian from '../../assets/images/zhongjianjian.svg';
import zujian from '../../assets/images/zujian.svg';
import huixuanbiao from '../../assets/images/huixuanbiao.svg';
import { getAppProgramCountApi, getAppSystemCountApi,getComponent } from "@/services/appSystem";
import { docCount,apiCount} from '@/services';
// 关键指标数据
const keyMetricsData = [
  { label: '用户数', value: '23,900', unit: '人' },
  { label: '纳管应用系统数', value: '73', unit: '个' },
  { label: '活跃用户数', value: '900', unit: '人' },
  { label: '已纳管应用系统及业务域', value: '6', unit: '个' },
  { label: '平均使用时长', value: '10\'43"', unit: '' }
];

// 功能模块数据
const functionModules = [
  {
    number: '01',
    title: '资产态',
    desc: '托管代码、配置、制品、文档等原始文件，形成软件资产'
  },
  {
    number: '02',
    title: '开发态',
    desc: '管理分支、构建环境、代码扫描、漏洞扫描，固化开发流程在研发工作台中，开箱即用'
  },
  {
    number: '03',
    title: '部署态',
    desc: '维护dev、test、uat、prod等环境的应用部署和中间件管理'
  },
  {
    number: '04',
    title: '运行态',
    desc: '查看运行状态、实例数、监控、告警、日志等信息'
  }
];

// 平台架构数据
const platformData = {
  unifiedServices: {
    title: '统一应用服务',
    items: ['租户管理和安全接入', '个性化界面', '交付待办', '统一桌面']
  },
  coreModules: [
    {
      title: '开发交付文档库',
      items: ['文档分类管理', '版本控制', '文档编辑上传', '文档在线预览', '文档反馈']
    },
    {
      title: '应用资产管理',
      items: ['应用分级分类', '应用基本信息', '应用注册', '应用变更']
    },
    {
      title: '应用初始化',
      items: ['前端脚手架', '后端脚手架', '应用套装模板', '应用测试与调优']
    },
    {
      title: '应用运行管理',
      items: ['应用部署', '应用日志', '应用性能诊断', '应用运营看板', '应用告警', '应用监控']
    },
    {
      title: '服务目录',
      items: ['应用共享组件', 'API市场', '组件市场']
    },
    {
      title: 'PaaS组件管理',
      items: ['中间件(Redis、Kafka、RabbitMQ、Tomcat)', '注册/配置中心', 'PaaS服务运行监控']
    }
  ],
  supportPlatforms: [
    '云管平台', '安全扫描', 'iPaaS', 'DevOps', '日志易', 'APM', 'TIS', 'CMDB'
  ],
  standards: [
    { icon: '目', text: '平台使用的相关标准和规范' },
    { icon: '运', text: '运营与运维相关标准和规范' }
  ]
};

// 底部功能特性数据
const bottomFeatures = [
  {
    icon: '+',
    iconClass: 'blue',
    title: '应用全生命周期管理',
    content: '应用创建（脚手架）、设计、开发（开发规范）及布上线，审批、监控，应用日常等生命周期管理'
  },
  {
    icon: 'S',
    iconClass: 'cyan',
    title: '统一服务开放',
    content: '容器化中间件和注册/配置中心，应用共享组件复用，统一API市场'
  },
  {
    icon: 'K',
    iconClass: 'gold',
    title: '知识沉淀',
    content: '一站式知识库，沉淀技术、产品、API、PaaS组件相关的文档和技术文档信息。'
  }
];


const HomePage = () => {
  // 统计卡片数据
  const [ statisticsCards, setStatisticsCards] = useState([
    { 
      id: 'yyxt',
      icon: yingyong,
      label: '应用系统',
      value: '0',
      unit: '个'
    },
    {
      id: 'yycx',
      icon: fuwu,
      label: '应用程序',
      value: '0',
      unit: '个'
    },
    {
      id: 'cpyfu',
      icon: zhongjianjian,
      label: '产品与服务',
      value: '5',
      unit: '个'
    },
    {
      id: 'zj',
      icon: zujian,
      label: '组件',
      value: '2',
      unit: '个'
    },
    {
      id: 'wd',
      icon: wendang,
      label: '文档',
      value: '50',
      unit: '个'
    },
    {
      id: 'api',
      icon: api,
      label: 'API',
      value: '上架中...',
      unit: ''
    }
  ]);
  
  useEffect(() => {
    Promise.all([
      getAppSystemCountApi(),
      getAppProgramCountApi(),
      docCount({startTime:'',endTime:''}),
      apiCount({startTime:'',endTime:''}),
      getComponent()
    ]).then(([sysRes, proRes,dcount,acount,ccount]) => {
      setStatisticsCards([
        { 
          id: 'yyxt',
          icon: yingyong,
          label: '应用系统',
          value: sysRes,
          unit: '个'
        },
        {
          id: 'yycx',
          icon: fuwu,
          label: '应用程序',
          value: proRes,
          unit: '个'
        },
        {
          id: 'cpyfu',
          icon: zhongjianjian,
          label: '产品与服务',
          value: '6',
          unit: '个'
        },
        {
          id: 'zj',
          icon: zujian,
          label: '组件',
          value: ccount,
          unit: '个'
        },
        {
          id: 'wd',
          icon: wendang,
          label: '文档',
          value: dcount,
          unit: '个'
        },
        {
          id: 'api',
          icon: api,
          label: 'API',
          value: acount,
          unit: '个'
        }
      ])
    }).catch(err => {
      console.log('获取统计卡片数据失败：',err);
    })
  },[])
  useEffect(() => {
   
  },[])
  return (
    <>
      <div className="home-container">
        {/* 页面标题 */}
        <div className="home-header">
          <div className="header-content">
            <h1>天合光能应用服务平台</h1>
            <div className="description">
              天合光能应用服务平台（TASP）是为天合应用系统建设者、管理者和IT管理者提供的一站式应用建设与集成管理平台。平台以提升应用系统建设与管理能效为目标，通过提供丰富的共享组件、标准的PaaS服务、系统化的交付文档库和自动化的应用全生命周期管理，减少研发人员学习成本、提高应用研发和部署效率，提升应用建设质量与效能，助力天合数字化转型。
            </div>
          </div>
        </div>
        <div className="content-wrapper">
          {/* 统计数据卡片 */}
          <div className='statistics-card-wrapper'>
            <div className="statistics-cards">
              {statisticsCards.map((card) => (
                <div className="stat-card" key={card.id}>
                  <div className="stat-icon">
                    <img src={card.icon} alt={`${card.label}图标`} style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
                  </div>
                  <div className="stat-content">
                    {
                      card.id==='api' && 
                      <>
                        <div className="stat-label">{card.label}</div>
                        <div className="stat-value-unit">
                          <div className="stat-value">{card.value}</div>
                          {
                            card.unit && <div className="stat-unit">（{card.unit}）</div>
                          }
                        </div>
                      </>
                    }
                    {
                      card.id==='wd' && 
                      <>
                        <div className="stat-label">{card.label}</div>
                        <div className="stat-value-unit">
                          <div className="stat-value">{card.value}<span className="stat-value-small"></span></div>
                          {
                            card.unit && <div className="stat-unit">（{card.unit}）</div>
                          }
                        </div>
                      </>
                    }
                    {
                      (card.id!=='api' && card.id!=='wd') && 
                      <>
                        <div className="stat-label">{card.label}</div>
                        <div className="stat-value-unit">
                          <div className="stat-value">{card.value}</div>
                          {
                            card.unit && <div className="stat-unit">（{card.unit}）</div>
                          }
                        </div>
                      </>
                    }
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 功能模块介绍 */}
          <div className="module-section">
            <div className="module-content">
              <div className="module-diagram">
                <img src={tubiaoImage} alt="功能模块示意图" style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
              </div>
              <div className="module-info">
                <div className="module-header">
                  <p className="module-description">
                  以应用为中心，从应用资产态、开发态、部署态、运行态来管理业务系统数据资产，将应用分散在各系统的关键数据汇集到应用管理平台，实现应用系统授权、统一开发、统一发布、统一运行管理的一体化管理！围绕应用全生命周期，实现应用建设更快、质量更高，让应用可视、可管、可控。
                  </p>
                </div>
                <div className="module-list">
                  {functionModules.map((module) => (
                    <div className="module-item" key={module.number}>
                      <div className="module-number">{module.number}</div>
                      <div className="module-info">
                        <div className="module-title">{module.title}</div>
                        <div className="module-desc">{module.desc}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 关键指标 */}
          <div className="key-metrics-section" style={{display:'none'}}>
            <div className="section-title">关键指标</div>
            <div className="metrics-content">
              <div className="metrics-list">
                {keyMetricsData.map((metric, index) => (
                  <div className="metric-item" key={index}>
                    <div className="metric-label">{metric.label}</div>
                    <div className="metric-value">
                      {metric.value}
                      {metric.unit && <span className="unit">{metric.unit}</span>}
                    </div>
                  </div>
                ))}
              </div>
              <div className="world-map">
                <img src={mapImage} alt="世界地图" />
              </div>
            </div>
          </div>

          {/* 平台架构 */}
          <div className="platform-section">
            <div className="section-title">平台架构</div>
            <div className="platform-content">
              {/* 统一应用服务 */}
              <div className="unified-services">
                <div className="service-title">{platformData.unifiedServices.title}</div>
                <div className="service-items">
                  {platformData.unifiedServices.items.map((item, index) => (
                    <div className="service-item" key={index}>{item}</div>
                  ))}
                </div>
              </div>

              {/* 核心功能模块 */}
              <div className="core-modules">
                {platformData.coreModules.map((module, index) => (
                  <div className="module-card" key={index}>
                    <div className="card-title">{module.title}</div>
                    <div className="card-items">
                      {module.items.map((item, itemIndex) => (
                        <div className="card-item" key={itemIndex}>{item}</div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* 底部支撑平台 */}
              <div className="support-platforms">
                {platformData.supportPlatforms.map((platform, index) => (
                  <div className="platform-item" key={index}>
                    <div className="lightning-icon">
                      <img src={huixuanbiao} alt="图标" style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
                    </div>
                    {platform}
                  </div>
                ))}
              </div>

              {/* 底部标准规范 */}
              <div className="standards">
                {platformData.standards.map((standard, index) => (
                  <div className="standard-item" key={index}>
                    <span className="icon">{standard.icon}</span>
                    {standard.text}
                  </div>
                ))}
              </div>
            </div>

            {/* 底部功能模块 */}
            {/* <div className="bottom-features">
              <div className="features-grid">
                {bottomFeatures.map((feature, index) => (
                  <div className="feature-card" key={index}>
                    <div className="card-header">
                      <span className={`icon ${feature.iconClass}`}>{feature.icon}</span>
                      <span className="title">{feature.title}</span>
                    </div>
                    <div className="card-content">
                      {feature.content}
                    </div>
                  </div>
                ))}
              </div>
            </div> */}
            
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default HomePage;
