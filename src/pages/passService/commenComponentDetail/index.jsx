import React, { useEffect, useRef, useState } from 'react';
import { useParams, useLocation, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import ParamsSetDrawer from '../passParamsSet';
import './index.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import dayjs from 'dayjs';
import axios from 'axios';
import { marked } from 'marked';
import MarkdownRenderer from '@/components/preview/MarkdownRenderer';
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

const STATUS_CONFIG = {
  0: {
    icon: LoadingOutlined,
    color: "#D1D5D9",
    text: "安装中"
  },
  1: {
    icon: CheckCircleFilled,
    color: "#1DC11D",
    text: "运行正常"
  },
  2: {
    icon: MinusCircleFilled,
    color: "#FAC800",
    text: "待安装"
  },
  3: {
    icon: ExclamationCircleOutlined,
    color: "#D93026",
    text: "不可用"
  },
  4: {
    icon: ExclamationCircleFilled,
    color: "#faad14",
    text: "运行异常"
  }
};

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle"
};

const ComponentDetail = () => {
  const location = useParams();
  const extraLocation = useLocation();
  // 从路由中获取 type 和 id 参数
  const { id } = location || {};
  const apiInfo = extraLocation.state.api || {}
  const [middlewareInfo, setMiddlewareInfo] = useState({
    id: 22,
    name: "postgresql",
    aliasName: "PostgreSQL",
    description: "CloudDB PostgreSQL数据库",
    type: "db",
    version: "16.4,16.1,14.13,14.7,14.2,13.8,13.6,13.5,12.10,11.15",
    image: null,
    imagePath: "postgresql-2.5.1.svg",
    status: null,
    createTime: "2025-01-14 11:30:37",
    versionStatus: "now",
    chartName: "postgresql",
    chartVersion: "2.5.1",
    grafanaId: null,
    replicas: null,
    replicasStatus: null,
    middlewares: null,
    official: true,
    vendor: "harmonycloud",
    enable: null,
    withMiddleware: null
  });
  const [middlewareMeta, setMiddlewareMeta] = useState({
    "appName": "PostgreSQL",
    "categoryName": "db",
    "vendor": "谐云科技",
    "usageCount": 49,
    "owner": null,
    "lastUpdateTime": "2025-01-14 11:30:37",
    "description": "CloudDB PostgreSQL数据库",
    "questionYaml": null
});

  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState(
    '<packaging>pom</packaging><organization><name>harmony cloud</name><url>https://www.harmonycloud.cn/</url></organization>');
  const [fileTree, setFileTree] = useState({});
  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [middlewareOperatorStatus, setMiddlewareOperatorStatus] = useState([]);
  const [markdownContent, setMarkdownContent] = useState('');
  const containerRef = useRef(null);
// //   // 获取中间件应用列表
//   useEffect(() => {
//     const fetchData = async () => {
//       try {
//         const versionRes = await getMiddlewareVersion({ 
//           clusterId: CLUSTER_ID, 
//           type: '' 
//         });
        
//         const middlewareInfo = versionRes.filter(item => item.id == id)[0];
//         setMiddlewareInfo(middlewareInfo);
//         const metaRes = await getMiddlewareMeta({ 
//           clusterId: CLUSTER_ID, 
//           type: '',
//           chartVersion: middlewareInfo.chartVersion 
//         });
//         setMiddlewareMeta(metaRes);
//         const operatorStatusRes = await getMiddlewareOperatorStatuss({ 
//           clusterId: CLUSTER_ID, 
//           type: middlewareInfo.name.toLocaleLowerCase(), 
//         });
//         setMiddlewareOperatorStatus(operatorStatusRes[0].status);
//         const fileListRes = await getMiddlewareFileList({ 
//           clusterId: CLUSTER_ID, 
//           type: middlewareInfo.chartName, 
//           chartVersion: middlewareInfo.chartVersion 
//         });
//         setFileTree(fileListRes);
//       } catch (error) {
//         console.error('获取中间件信息失败:', error);
//       }
//     };

//     fetchData();
//   }, []);

  const handleBack = () => {
    history.back();
  };

  const formattedCreateTime = (time) => {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  } 

  useEffect(() => {
    const fetchMarkdown = async () => {
      try {
        const parsedUrl = new URL(apiInfo.url);
        const response = await axios.get(parsedUrl.pathname);
        const text = response.data || '';
        setMarkdownContent(text);
      } catch (error) {
          console.error('Error fetching Markdown file:', error);
      }
    };

    fetchMarkdown();
    
  }, [apiInfo.url]);

  useEffect(() => {
    if (apiInfo.fileType === 'html' && containerRef.current) {
      let shadowRoot = containerRef.current.shadowRoot;
      // 如果没有影子根，则创建一个
      if (!shadowRoot) {
          shadowRoot = containerRef.current.attachShadow({ mode: 'open' });
      }
      // 更新影子根内的内容
      shadowRoot.innerHTML = markdownContent;
    }
  }, [apiInfo, markdownContent]);
  // const handleFileClick = async (fileName) => {
  //   setSelectedFile(fileName);
  //   const fileContentRes = await getMiddlewareFileContent({ 
  //     clusterId: CLUSTER_ID, 
  //     type: middlewareInfo.chartName, 
  //     chartVersion: middlewareInfo.chartVersion,
  //     fileName: fileName
  //   });
  //   setFileContent(fileContentRes);
  // };

//   const renderFileTree = (items) => {
//     if (!items || !items.children) return null;
//     return (
//       <ul className="file-tree">
//         {items.children.map((item) => (
//           <li key={item.fileName} className={item.directory ? 'folder' : 'file'}>
//             <span onClick={() => !item.directory && handleFileClick(item.currentDirectory)}>
//               {item.fileName}
//             </span>
//             {item.directory && renderFileTree(item)}
//           </li>
//         ))}
//       </ul>
//     );
//   };

//   const renderStatus = (value) => {
//     const config = STATUS_CONFIG[value];
//     if (!config) return "/";
    
//     const Icon = config.icon;
//     return (
//       <>
//         <Icon style={{ ...iconStyle, color: config.color }} />
//         {config.text}
//       </>
//     );
//   };

  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header dealPadding">
        <div className="nav-path">
          <span>产品与服务</span>
          <span>/</span>
          <span>共享组件详情</span>
        </div>
        <div className="back-button" onClick={handleBack}>
          <LeftOutlined />
          <span>返回</span>
        </div>
      </div>
      <div className="info-section dealPadding">
        <h3 className="section-title">基础信息</h3>
        <div className="info-grid">
          <div className="info-item">
            <span className="label">名&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;称：</span>
            <span className="value">{apiInfo.name}</span>
          </div>
          <div className="info-item">
            <span className="label label-3">版&nbsp;&nbsp;&nbsp;&nbsp;本：</span>
            <span className="value">{apiInfo.version}</span>
          </div>
          <div className="info-item">
            <span className="label">文档链接：</span>
            <span className="value">
              <a href={apiInfo.url} target="_blank" rel="noopener noreferrer">
                {apiInfo.url}
              </a>
            </span>
          </div>
          <div className="info-item">
            <span className="label label-3">创建人：</span>
            <span className="value">{apiInfo.creator}</span>
          </div>
          <div className="info-item">
            <span className="label">创建时间：</span>
            <span className="value">{formattedCreateTime(apiInfo.createTime)}</span>
          </div>
          <div className="info-item">
            <span className="label label-3">修改人：</span>
            <span className="value">{apiInfo.updater}</span>
          </div>
          <div className="info-item">
            <span className="label">修改时间：</span>
            <span className="value">{formattedCreateTime(apiInfo.updateTime)}</span>
          </div>
          <div className="info-item full-width">
            <span className="label">描&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;述：</span>
            <span className="value">{apiInfo.description}</span>
          </div>
        </div>
      </div>
      <div className="info-section dealPadding" style={{marginTop:'0px'}}>
        <h3 className="section-title">使用说明</h3>
          <div className="file-content" >
            <div>
              {apiInfo.fileType === 'md' && <MarkdownRenderer content={markdownContent} />}
              {apiInfo.fileType === 'html' && 
                <div className="w-full h-full overflow-auto" ref={containerRef}>
                </div>}
            <div>
        </div>
            </div>
            {/* <CodeMirror
              value={htmlContent}
              height="200px"
              theme="light"
              extensions={[html()]}
              editable={false}
              readOnly={true}
              basicSetup={{
                lineNumbers: true,
                foldGutter: true,
                highlightActiveLine: true,
                dropCursor: true,
                allowMultipleSelections: true,
                indentOnInput: true,
                
              }}
              style={{ overflow: 'auto' }}
            /> */}
          </div>
      </div>
     
    </div>
  );
};

export default ComponentDetail; 