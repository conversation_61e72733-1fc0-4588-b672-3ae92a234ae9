
#root-master .page-header {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	z-index: 999;
	padding: 0 40px;
	height: @header-height;
	min-height: @header-height;
	box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.08);
	.left {
		height: 100%;
		display: flex;
		align-items: center;
		margin-right: 10px;
		flex: 1;
		min-width: 0;
		.logo-box{
			height: 100%;
			cursor: pointer;
			display: flex;
			align-items: center;
			width: 258px;
			transition: width 0.3s ease-in-out;
			.logo {
				width: 94px;
				min-width: 94px;
				overflow: hidden;
				.logo-img {
					height: 100%;
					display: flex;
					justify-content: center;
					img {
						width: 100%;
					}
				}
			}
			h4 {
				font-size: @font-size-h1;
				color: @text-primary-color;
				margin-left: 20px;
				font-weight: normal;
				margin-bottom: 0;
				white-space: nowrap;
			}
			&.logo-only {
				width: 94px;
				h4 {
					display: none	;
				}
			}
		}
		
		.topMenu {
			height: 100%;
			display: flex;
			flex: 1;
			align-items: center;
			margin-left: 4%;
			min-width: 0;
			overflow: hidden;
			.menus-ul {
				height: 32px;
				line-height: 32px;
				margin-bottom: 0px;
				font-size: 13px;
				li {
					cursor: pointer;
				}
			}
			>div.item {
				margin-right: 20px;
				cursor: pointer;
				font-size: 14px;
				border-radius: 4px;
				display: flex;
				align-items: center;
				color: #4E5969;
				.hc-icon {
					font-size: 20px;
					margin-right: 4px;
				}
				.name {
					display: flex;
					align-items: center;
					.active_svgImg {
						width: 20px;
						height: 20px;
						margin-right: 4px;
						div {
							width: 100%;
							height: 100%;
							display: flex;
						}
						svg {
							width: 20px;
							height: 20px;
						}
					}
				}
				&.dropdown-item {
					display: flex;
					.arrow {
						.hc-icon {
							margin-right: 0;
						}
					}
					&:hover,
					&:focus {
						.arrow {
							.hc-icon {
								transition: all 0.5s;
								transform: rotate(180deg);
							}
						}	
					}
				}
			}
			>div.item:last-child {
				margin-right: 0;	
			}
			.micro-divider-vertical {
				margin: 0 8px 0 0;
			}
			.right-most-menu {
				margin-left: auto;
			}
		}
	}
	.right {
		display: flex;
		align-items: center;
		height: 100%;
		flex: 0 0 auto;
		.operate {
			height: 100%;
			display: flex;
			align-items: center;
			color: #666666;
			font-size: @font-size-normal;
			.back-manage-btn{
				color: @text-secondary-color;
				padding: 0;
			}
			.back-manage-btn:hover{
				color: @primary-color;
			}
			.micro-divider{
				margin: 0 10px;
				height: 30px;
			}
			.project-switcher {
				.project-name-box {
					display: flex;
					align-items: center;
					cursor: pointer;
					font-size: 13px;
					b {
						font-weight: 500;
						padding-right: 4px;
					}
					.hc-icon {
						font-size: 16px;
						margin-right: 2px;
					}
					.projectName {
						max-width: 200px;
						display: block;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						}
				}
				&:active {
					color: #2869F6;
				}
			}
			.front-back-end {
				font-size: 20px;
				cursor: pointer;
				padding: 0 10px;
			}
			.org-switch-item{
				font-size: 0.85em;
				width: 114px;
				line-height: 18px;
				display: flex;
				align-items: center;
				// margin-right: 16px;
				cursor: pointer;
			}
			.account-info {
				cursor: pointer;
				.userName {
					display: flex;
					align-items: center;
					font-size: 16px;
					font-family: 'PingFangSC-Regular, PingFang SC';
					color: #000;
					.name {
						margin-left: 15px;
					}
					dl {
						padding-left: 8px;
						color: rgba(0, 0, 0, 0.65);
						margin: 0 !important;
						dt {
							font-weight: normal;
							max-width: 114px;
							line-height: 18px;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
							&:active{
								color: #2869F6;
							}
						}
						dd {
							margin: 0 !important;
						}
					}
					.name-icon {
						width: 32px;
						height: 32px;
						box-sizing: content-box;
						border-radius: 50%;
						overflow: hidden;
						background: rgba(40, 105, 246, 0.10);
						text-align: center;
						font-size: 0.85em;
						font-weight: 500;
						line-height: 32px;
						color: #2869F6;
						border: 4px solid #FFF;
						&:hover,
						&:focus {
							border-color: #F7F7F7;
						}
						&:active{
							border-color: #efe9fd;
						}
					}
					&.active {
						.name-icon {
							border-color: #F7F7F7;
						}
					}
				}
			}
		}
		.notice {
			position: relative;
			cursor: pointer;
			margin-right: 10px;
			width: 20px;
			height: 14px;
			.fa-bell {
				font-size: 20px;
				color: #666666;
			}
			.notice-count {
				position: absolute;
				top: -10px;
				right: -8px;
				background-color: red;
				color: white;
				font-size: 12px;
				padding: 2px 6px;
				border-radius: 50%;
			}

			.notice-dropdown {
				position: absolute;
				top: 100%;
				right: 0;
				background-color: white;
				border: 1px solid #ccc;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				width: 200px;
				z-index: 1;
			}

			.notice-item {
				padding: 10px;
				border-bottom: 1px solid #eee;
			}
		}
	}
}

.information-header {
	height: 14px;
	line-height: 14px;
	border-left: 2px solid @primary-color;
	color: #333;
	font-size: 14px;
	font-weight: 500;
	padding-left: 8px;
}

.global-project-list{
	background: #fff;
	box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.08);
	min-width: 550px;
	max-width: 872px;
	max-height: 486px;
	overflow-y: auto;
	border-radius: 2px 0px 0px 0px;
	margin-top: 10px;
	padding: 20px 10px;
	.global-project-list-header {
		padding-left: 10px;
		display: flex;
		height: 32px;
		justify-content: space-between;
		align-items: center;
		.information-header {
			margin-top: 9px;
		}
		.search-box {
			width: 280px;
		}
		.micro-input-affix-wrapper {
			background: rgba(0, 0, 0, 0.03);
		}
	}
	.global-project-list-box {
		display: flex;
		flex-wrap: wrap;
		max-height: 300px;
		overflow-y: auto;
		p {
			color: rgba(0, 0, 0, 0.65);
			font-size: 12px;
			cursor: pointer;
			padding: 10px;
			margin: 5px;
			border-radius: 4px;
			&:hover {
				background: rgba(0, 0, 0, 0.03);
			}
			&.active {
				background: #2869f61a;
				color: rgba(40, 105, 246, 1);
				max-width: 100px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
}
.global-account-info{
	background: #fff;
	border-radius: 4px;
	color: rgba(0, 0, 0, 0.65);
	box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.08);
	width: 252px;
	padding: 20px 20px 8px;
	margin-top: 8px;
	font-size: 12px;
	.account-box {
		display: flex;
		align-items: center;
		margin-bottom: 16px;
		.thumbnail {
			width: 42px;
			height: 42px;
			border-radius: 42px;
			line-height: 42px;
			background: rgba(40, 105, 246, 0.1);
			color: rgba(40, 105, 246, 1);
			font-size: 16px;
			text-align: center;
		}
		.account-name {
			width: calc(100% - 52px);
			margin-left: 10px;
			.show-name {font-weight: bold;}
			.user-name {color: rgba(0, 0, 0, 0.3);}
		}
	}
	.operate-item {
		cursor: pointer;
		border-radius: 4px;
		font-size: 13px;
		padding: 6px 10px;
		&:focus,
		&:hover {
			background: rgba(0, 0, 0, 0.03);
		}
		&:active {
			background: rgba(40, 105, 246, 0.10);
			color: #2869F6;
		}
	}
	.operate-item.tenant {
		padding: 0;
	}
	.micro-divider-horizontal {
		margin: 8px 0px;
	}
	.org-switch-item {
		// margin-top: 20px;
		cursor: pointer;
	}
	.switch-language {
		display: flex;
		justify-content: space-between;
		.key {
			.hc-icon {
				font-size: 16px;
			}
		}
		.val {
			.hc-icon {
				font-size: 20px;
			}
		}
		&:hover, 
		&:focus {
			.val {
				.hc-icon {
					transition: all 0.5s;
					transform: rotate(90deg);
				}
			}
		}
	}
	.logout {
		text-align: center;
	}
}
.org-switch-item {
	background: rgba(40, 105, 246, 0.03);
	border: 1px solid rgba(40, 105, 246, 0.02);
	border-radius: 4px;
	padding: 6px 10px;
	display: flex;
	justify-content: space-between;
	&:hover {
		border: 1px solid rgba(40, 105, 246, 1);
		.switch-icon {
			color: #2869F6
		}
	}
	p {
		width: calc(100% - 14px);
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
}
.header-switch-list-box {
	.micro-popover-content {
		.micro-popover-inner {
			font-size: 13px;
			padding: 5px;
			.micro-popover-inner-content {
				width: 100%;
				padding: 0;
			}
			.account-orglist-warp {
				list-style: none;
				margin: 0;
				padding: 0;
				overflow-y: auto;
				max-height: 300px;
			}
			li {
				color: rgba(0, 0, 0, 0.65);
				padding: 5px 10px;
				margin-top: 5px;
				cursor: pointer;
				border-radius: 4px;
				&:first-child{
					margin-top: 0;
				}
				&:hover,
				&:focus {
					background: rgba(0, 0, 0, 0.03);
				}
				&:active {
					color: #2869F6;
					background: rgba(40, 105, 246, 0.10);
				}
				&.active {
					background: rgba(40, 105, 246, 0.10);
					color: #2869F6;
					font-weight: 500;
				}
			}
		}
	}
	&.language-switch {
		.micro-popover-content {
			.micro-popover-inner{
				width: 86px;
				margin-right: 20px;
			}
		}
	}
	&.organization-switch {
		.micro-popover-content {
			.micro-popover-inner{
				width: 240px;
				margin-top: 8px;
			}
		}
	}
	&.organization-sub-switch {
		.micro-popover-content {
			margin-right: 8px;
			.micro-popover-inner{
				width: 150px;
			}
		}
	}
	&.micro-popover-placement-left, 
	&.micro-popover-placement-leftTop, 
	&.micro-popover-placement-leftBottom,
	&.micro-popover-placement-bottom, 
	&.micro-popover-placement-bottomLeft, 
	&.micro-popover-placement-bottomRight {
		padding: 0;
	}
}
.header-tooltip {
	.micro-tooltip-inner {
		color: rgba(0, 0, 0, 0.65);
	}
	&.micro-tooltip-placement-bottom, 
	&.micro-tooltip-placement-bottomLeft, 
	&.micro-tooltip-placement-bottomRight {
		padding: 12px 0 0 ;
	}
}

.products-dropdown {
	padding-top: 8px;
	.micro-dropdown-menu {
		border-radius: 4px;
		max-width: 472px;
		display: flex;
		flex-wrap: wrap;
		padding: 16px;
		gap: 16px 8px;
		.micro-dropdown-menu-item {
			font-size: 13px;
			flex-direction: column;
			padding: 12px;
			min-width: 86px;
			max-width: 102px;
			width: 25%;
			.hc-icon {
				font-size: 32px;
				margin-bottom: 12px;
			}
			&:active {
				color: #2869F6;
				background: rgba(40, 105, 246, 0.10);
			}
			.micro-dropdown-menu-title-content {
				word-break: break-word;
				text-align: center;
			}
		}
		.micro-dropdown-menu-item-icon {
			margin-right: 0;
		}
		.svgIcon {
			margin-bottom: 12px;
			div, svg {
				width: 32px;
				height: 32px;
			}
		}
	}
	&.dropdown-item-center {
		.micro-dropdown-menu {
			justify-content: center;
		}
	}
}
.icon-operate-item {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 4px;
	cursor: pointer;
	margin: 0 4px;
	&:hover,
	&:focus{
		background: rgba(0, 0, 0, 0.03);
	}
	&:active {
		color: #2869F6;
		background: rgba(40, 105, 246, 0.10);
	}
	.hc-icon {
		font-size: 20px;
	}
}

.notice-dropdown {
	background: #f9fafb;
	border-radius: 4px;
	color: rgba(0, 0, 0, 0.65);
	box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.08);
	width: 373px;
	height: 676px;
	padding: 20px 0 8px;
	margin-top: 8px;

	.notice-list {
		height: 500px;
		background: #fff;
		overflow: auto;
		padding: 0 20px;

		.micro-list {
			.micro-list-item {
				.micro-list-item-meta {
					.micro-list-item-meta-avatar {
						margin-inline-end: 10px
					}
				}
			}
		}
	}
}
// .org-switch-list-sub-box {
// 	.org-switch-list-box;
	
// 	.micro-popover-content {
// 		.micro-popover-inner{
// 			width: 150px;
// 		}
// 	}
// }
#root-master {
	.micro-menu-root.micro-menu-horizontal{
		line-height: @header-height;
		.micro-menu-item{
			font-size: @font-size-h4;
			color: @text-primary-color;
		}
		.micro-menu-item.micro-menu-item-selected{
			color: @primary-color;
			font-weight: 600;
		}
		.micro-menu-item.micro-menu-item-selected::after,
		.micro-menu-item.micro-menu-item-active::after{
			border-bottom-color: @primary-color;
		}
		.micro-menu-overflow-item-rest::after{
			border: none;
		}
		.micro-menu-submenu-active > .micro-menu-submenu-title{
			color: @primary-color !important;
		}
	}
	.back-end-top-menu.micro-menu-root.micro-menu-horizontal{
		.micro-menu-item.micro-menu-item-selected,
		.micro-menu-item.micro-menu-item-active{
			color: @primary-color;
		}
		.micro-menu-item::after,
		.micro-menu-item::after{
			// border: none!important;
		}
	}
}
.micro-menu-submenu{
	.micro-menu .micro-menu-item-selected{
		color: @primary-color;
	}
}