import React from "react";
import Logo from "./logo";
import TopMenu from "./topMenu";
import Operate from "./operate";
import Notice from "./notice";

import "./index.less";

function Header({isHome}) {
  return (
    <div className="page-header">
      <div className="left">
        <Logo isHome={isHome}/>
        <TopMenu isHome={isHome}/>
      </div>
      <div className="right">
        {/* <Notice isHome={isHome}></Notice> */}
        <Operate isHome={isHome}/>
      </div>
    </div>
  );
}

export default React.memo(Header);
