import { useEffect, useState } from "react";
import { useModel } from "umi";
import { Popover, Tree, message } from "antd";
import { SwapOutlined } from "@ant-design/icons";
import { getTenantTree, changeTenant } from "@/services";

export default function TenantChange() {
  const { userInfo, userDetailInfo } = useModel("user");
  const [tenantList, setTenantList] = useState([]);
  
  useEffect(() => {
    if(userInfo){
      getTenantTree().then((res) => {
        if(res) setTenantList(res ?? []);
      });
    }
  }, [userInfo]);

  const handleChangeTenant = (node) => {
    const { key } = node
    if(key ===  userInfo.tenantId){
      return
    }
    changeTenant(key).then((res) => {
      if(res){
        window.localStorage.clear();
        window.sessionStorage.clear();
        window.location.reload();
      }else{
        message.error("切换租户失败")
      }
    }).catch(() => {
      message.error("切换租户失败")
    })
  };

  // 生成 treeData 的递归函数
  const generateTreeData = (data, tenantGroup) => {
    return data?.map(item => {
      const ippeNoAuth = (userInfo?.appCode === 'ippe' || userInfo?.appCode === 'local-app') && 
                        !tenantGroup.includes(String(item.id));
      const childrenHasAuth = item?.children?.some(d => tenantGroup.includes(String(d.id)));
      
      const color = (ippeNoAuth && !childrenHasAuth) ? 'rgba(0,0,0,0.25)' : 'rgba(0,0,0,0.65)';
      
      return {
        key: item.id,
        title: (
          <span style={{ color }}>
            {!item?.children?.length && (
              <span style={{
                display: 'inline-block',
                width: 6,
                height: 6,
                borderRadius: 3,
                backgroundColor: color,
                marginRight: 6,
                marginBottom: 2,
              }}></span>
            )}
            {item.name}
          </span>
        ),
        disabled: !tenantGroup.includes(String(item.id)),
        children: item.children ? generateTreeData(item.children, tenantGroup) : undefined,
        dataRef: item,  // 保留原始数据引用
      };
    });
  };

  const treeData = generateTreeData(
    tenantList, 
    (userDetailInfo?.tenantGroup || '').split(',')
  );
//TODO: 租户显示与否控制需完善
  return userInfo ? (
    <Popover
      trigger={["click"]}
      zIndex={5000}
      content={
        <div>
          <Tree
            height={450}
            treeData={treeData}
            style={{ fontSize: '13px' }}
            blockNode
            // defaultExpandAll
            onSelect={(selectedKeys, { node }) => handleChangeTenant(node)}
          />
        </div>
      }
      arrow={false}
      placement="left"
      overlayClassName="header-switch-list-box organization-switch"
    >
      <div
        className="org-switch-item"
        onClick={(e) => e.stopPropagation()}
      >
        <p>{userInfo?.tenantName}</p>
        <SwapOutlined className="switch-icon" />
      </div>
    </Popover>
  ) : null;
}