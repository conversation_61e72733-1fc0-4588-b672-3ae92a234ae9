import { useState, useEffect } from "react";
import { useModel, history, useLocation } from "umi";
import {
  Dropdown,
  Image,
  Divider,
  Popover,
  notification,
  Button,
  message
} from "antd";
import avatarImg from "@/assets/images/user-avatar.svg";
import locale from "@/locale";
import ModifyPassword from "@/pages/login/modifyPassword";
import storage from "@/utils/storage";
import TenantChange from './TenantChange/index'
import {
  changeUserPassword,
  loginOut,
  getUserInfo
} from "@/services";
import { localStorageEnum } from '@/enums/storageEnum';
import { homePath } from "@/constants"
import "./index.less";
// 头部全局操作区
export default function Operate({isHome}) {

  const { token, userInfo, setUserInfoStore, clearUserInfoStore, clearTokenStore } = useModel("user");
  const { isShowAdminEntry } = useModel("sysConfig");
  const [passwordModelVisible, setPasswordModelVisible] = useState(false);
  const [accountVisible, setAccountVisible] = useState(false);
  const [curLanguage, setCurLanguage] = useState(
    storage.getLocal("language") || "zh-CN"
  );

  const USERINFO = localStorageEnum.USERINFO; // 保存用户信息
  const languages = [
    { key: "zh-CN", label: "简体中文" },
    { key: "zh-HK", label: "繁體中文" },
    { key: "en-US", label: "English" },
  ];
  
  // 跳转到后台管理系统
  const handleManageOrHomeEntry = () => {
    const pathname = isHome ? '/backend' : homePath;
    history.push({
      pathname
    });
  }

  // 登录
  const handleLogin = async () => {
    try {
      const res = await getUserInfo();
      if(res){
        setUserInfoStore(res)
        storage.setLocal(USERINFO, res)
      }
    } catch (error) {
      console.log(error);
    }
  }

  // 退出登录
  const logout = async () => {
    try{
      const res = await loginOut()
      if(res){
        clearTokenStore();
        clearUserInfoStore();
        if(typeof res === "string" ){// 天合退出登录需要重定向一个地址进行退出登录
          window.location.href = res
        }else{
          handleLogin()
        }
      }
    }catch(err){
      message.error("退出登录失败")
      console.log(err);
    }
  };

  // TODO：修改密码
  const changePassword = (data) => {
    changeUserPassword(data, token).then(() => {
      setPasswordModelVisible(false);
      notification.success({
        message: locale.notice.success,
        description: locale.user.pwd_modify_success,
      });
      setTimeout(() => {
        logout();
      }, 1000);
    });
  };

  // 头像下拉
  const accountInfo = () => {
    return (
      <div className="global-account-info">
        <div className="account-box">
          <p className="thumbnail">{userInfo?.userRealname?.substr(0, 1).toUpperCase() || ""}</p>
          <div className="account-name">
            <p className="show-name">{userInfo?.orgName || ""}</p>
            <p className="user-name">{userInfo?.userRealname || ""}</p>
          </div>
        </div>
        <Divider />
        <div className="operate-item tenant">
          <TenantChange/>
        </div>
        <Divider />
        { 
          isShowAdminEntry && (
            <>
              <div
                onClick={(e) => {
                  handleManageOrHomeEntry();
                  setAccountVisible(false);
                  e.stopPropagation();
                }}
                className="operate-item"
              >
                {isHome ? '后台管理' : '平台首页'}
              </div>
              <Divider />
            </>
          )
        }
        {/* <div
          onClick={(e) => {
            setPasswordModelVisible(true);
            setAccountVisible(false);
            e.stopPropagation();
          }}
          className="operate-item"
        >
          {locale.header.modify_password}
        </div>
        <Divider /> */}
        {/* {window.isSupportLanguageSwitch && (
          <>
            <Popover
              content={
                <ul className="account-orglist-warp">
                  {languages.map((item) => (
                    <li
                      key={item.key}
                      className={item.key === curLanguage ? "active" : ""}
                      onClick={() => {
                        setCurLanguage(item.key);
                        storage.setLocal("language", item.key);
                        window.location.reload();
                      }}
                    >
                      {item.label}
                    </li>
                  ))}
                </ul>
              }
              arrow={false}
              placement="leftTop"
              overlayClassName="header-switch-list-box language-switch"
            >
              <div className="switch-language operate-item">
                <div className="key">
                  {locale.header.switch_language}
                </div>
                <div className="val">
                  {curLanguage === "zh-CN"
                    ? "简体中文"
                    : curLanguage === "zh-HK"
                    ? "繁體中文"
                    : "English"}
                </div>
              </div>
            </Popover>
            <Divider />
          </>
        )} */}

        <div onClick={logout} className="logout operate-item">
          {locale.header.logout}
        </div>
      </div>
    );
  };
  
  return (
    <div className="operate">
      { userInfo ? 
        <div className="account-info">
          <Dropdown
            trigger={["click"]}
            placement="bottomRight"
            open={accountVisible}
            dropdownRender={() => {
              return accountInfo();
            }}
            onOpenChange={setAccountVisible}
          >
            <div className={`userName ${accountVisible ? "active" : ""}`}>
              {userInfo?.userRealname ? (
                <div className="name-icon">
                  {userInfo.userRealname?.substr(0, 1).toUpperCase()}
                </div>
              ) : (
                <Image width={24} preview={false} src={avatarImg} />
              )}
              <span className="name">{userInfo?.userRealname}</span>
            </div>
          </Dropdown>
        </div> :
        <Button type="link" onClick={handleLogin}>登 录</Button>
      }
      <ModifyPassword
        visible={passwordModelVisible}
        setVisible={setPasswordModelVisible}
        onFinish={changePassword}
      />
    </div>
  );
}
