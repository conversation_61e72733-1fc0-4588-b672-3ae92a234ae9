import { useState, useEffect } from "react";
import { useModel, history, useLocation } from "umi";
import {
  Dropdown,
  Image,
  Divider,
  Popover,
  notification,
  Button,
  message,
  Typography,
  Radio,
  List
} from "antd";
import "./index.less";
import Icon, { BellOutlined, CheckOutlined } from "@ant-design/icons";

const mockNotifications = [
    { id: 1, title: '通知标题1', content: '这是通知的具体内容1这是通知的具体内容1这是通知的具体内容1', time: '5分钟前', read: false },
    { id: 2, title: '通知标题2', content: '这是通知的具体内容2', time: '1小时前', read: true },
    { id: 3, title: '通知标题1', content: '这是通知的具体内容1', time: '5分钟前', read: false },
    { id: 4, title: '通知标题2', content: '这是通知的具体内容2', time: '1小时前', read: true },
    { id: 5, title: '通知标题1', content: '这是通知的具体内容1', time: '5分钟前', read: false },
    { id: 6, title: '通知标题2', content: '这是通知的具体内容2', time: '1小时前', read: true },
  // 可以添加更多通知
];

export default function Operate({isHome}) {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [activeTab, setActiveTab] = useState('全部');

    const handleSetAllRead = () => {
        // 这里可以添加将所有通知设为已读的逻辑
        console.log('全部设为已读');
    };

    const handleTabChange = ({ target: { value } }) => {
        setActiveTab(value);
    };

    const getFilteredNotifications = () => {
        if (activeTab === '未读') {
        return mockNotifications.filter(notification => !notification.read);
        }
        return mockNotifications;
    };

    const handleAllNotice = () => {
        console.log('跳转全部通知页面')
    }

    const overlay = () => {
        const filteredNotifications = getFilteredNotifications();
        return (
            <div className="notice-dropdown">
                <div style={{ display: 'flex', alignItems: 'center', padding: '0 20px' }}>
                    <Typography.Title level={3} style={{ margin: 0, fontWeight: 500, flex: 1 }}>通知</Typography.Title>
                    <span style={{ color: '#2b7fff', float: 'right', cursor: 'pointer' }} onClick={handleSetAllRead}>
                        全部设为已读
                    </span>
                </div>
                <div className="notice-tabs" style={{padding: '20px 40px'}}>
                    <Radio.Group
                        style={{width: '100%'}}
                        defaultValue={activeTab} 
                        buttonStyle="solid"
                        onChange={handleTabChange}
                    >
                        <Radio.Button style={{width: '50%', textAlign: 'center'}} value="全部">全部</Radio.Button>
                        <Radio.Button style={{width: '50%', textAlign: 'center'}} value="未读">未读</Radio.Button>
                    </Radio.Group>
                </div>
                <div className="notice-list">
                    <List
                        dataSource={filteredNotifications}
                        renderItem={(notification) => (
                            <List.Item
                                key={notification.id}
                                style={{ backgroundColor: notification.read ? 'transparent' : '#eff6ff', 
                                    border: notification.read ? '1px solid #eee': '1px solid #c6dfff',
                                    borderRadius: '8px',
                                    marginBottom: '10px'
                                }}
                            >
                                <List.Item.Meta
                                    avatar={
                                        <div style={{ color: notification.read ? 'green' : 'blue', marginLeft: '10px', marginTop: '7px', fontSize: '10px' }}>
                                            {notification.read ? <CheckOutlined /> : <div style={{ width: 10, height: 10, borderRadius: '50%', backgroundColor: '#2b7fff' }} />}
                                        </div>
                                    }
                                    title={<strong style={{fontSize: '16px'}}>{notification.title}</strong>}
                                    description={
                                        <div>
                                            <div style={{fontSize: '16px'}}>{notification.content}</div>
                                            <div>{notification.time}</div>
                                        </div>
                                    }
                                />
                            </List.Item>
                        )}
                    />
                </div>
                <div style={{width: '100%',textAlign: 'center', color: '#2b7fff', height: '46px', lineHeight: '46px'}}>
                    <span onClick={handleAllNotice} style={{cursor: 'pointer'}}>查看全部通知</span>
                </div>
            </div>
        );
    };

    return(
        <div className="notice">
            <Dropdown
                trigger={["click"]}
                placement="bottomCenter"
                open={isDropdownOpen}
                onOpenChange={setIsDropdownOpen}
                dropdownRender={() => {
                    return overlay();
                }}
            >
                <span className="ant-dropdown-link" onClick={(e) => e.preventDefault()}>
                    <BellOutlined className='fa fa-bell' />
                    {mockNotifications.length > 0 && (
                        <span className="notice-count">{mockNotifications.length}</span>
                    )}
                </span>
            </Dropdown>
            
        </div>
    )
}