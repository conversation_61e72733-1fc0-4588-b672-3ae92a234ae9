import locale from "@/locale";
import React, { useState } from "react";
import { Image } from "antd";
import { useModel, history } from "umi";
import { homePath } from "@/constants"

export default function Logo() {
  const { sideBarExpand } = useModel("menu", ({sideBarExpand}) => ({
    sideBarExpand
  }))
  
  const { initialState } = useModel("@@initialState");
  const { appConfig } = initialState;
  
  const handleClick = () => {
    history.push(homePath);
  }
  return (
    <div className={`logo-box ${sideBarExpand ? '' : 'logo-only'}`} onClick={handleClick}> 
      <div className="logo">
        <Image src={appConfig?.logoUrl} preview={false} rootClassName="logo-img"/>
      </div>
      <h4>{appConfig?.title}</h4>
    </div>
  );
}
