// export default platTableCard;
import React, { useEffect, useRef, useState } from "react";
import {Badge ,Input,Button,message,Col, Row,Select,Table,Card  } from 'antd';
import {
    DisconnectOutlined
  } from '@ant-design/icons';
  import  platTitle from '@/assets/images/platTitle.png';
  import SelectSearch from './SelectSearch';
  import { useModel } from 'umi';
import { size } from "lodash";
import Demo from './Demo';
import debounce from 'lodash/debounce';
import { BaseUrlEnum } from '@/enums/httpEnum';
import axios from 'axios';
const platTableCard = ({info,title,setSelUser,commitInfo}) => {
   
      const [searchText, setSearchText] = useState('');
      // const [filteredUsers, setFilteredUsers] = useState([]);
      const { userInfo } = useModel('user');
      const [userName, setUserName] = useState(userInfo.id);
      const [data, setData] = useState([]);
   
      const columns = [
        {
          title: '应用系统名称',
          dataIndex: 'projectName',
          key: 'projectName',
          render: text => <span>{text}</span>,
        },
        {
          title: '提交总次数',
          dataIndex: 'commitCount',
          key: 'commitCount',
          render: text => <span>{text}</span>,
        },
        {
          title: '首次提交时间',
          dataIndex: 'firstCommitTime',
          key: 'firstCommitTime',
          render: text => <span>{text}</span>,
        }
      ]
     
      ////开始
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);

  const fetchRef = useRef(0);

  const [value, setValue] = useState(userInfo.id);

// 加载初始值对应的label
const fetchOptions = async (keyword) => {
  try {

      const response = await axios.get(`${BaseUrlEnum.KEPLER}/upms/u/users`, {
        params: { name: keyword,current:1,size:10,isQueryLocked:1 }
      });
      setData(response.data.data.records)
      return response.data.data.records.map(user => ({
        value: user.id,
        label: user.userRealname
      }));
    }catch (error) {
    console.error('Fetch users error:', error);
    return [];
  }
};
  useEffect(() => {
    if (userInfo && !options.find(o => o.value === userInfo.id)) {
      fetchOptions(userInfo.userRealname).then((newOptions) => {
        setOptions(prev => [...prev, ...newOptions]);
      }).catch(() => {
        message.error('加载初始数据失败');
      });
    }
  }, [userInfo]);

  const onChange = (value) => {
    setUserName(value);
     const filteredItems = data.filter(item => item.id === value);
     setSelUser(filteredItems);
     
   }
  const debounceFetcher = React.useMemo(() => {
    const loadOptions = (value) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);
      
      fetchOptions(value)
        .then((newOptions) => {
          if (fetchId !== fetchRef.current) {
            return;
          }
          setOptions(newOptions);
          setFetching(false);
        })
        .catch(() => {
          message.error('加载选项失败');
          setFetching(false);
        });
    };

    return debounce(loadOptions, 1000);
  }, [fetchOptions, 1000]);

 
  return (
    <div className="platformBoard-content-box" style={{width:'50%',marginRight:'0px',height:'100%'}}>
    <Card style={{ width: '100%' ,marginRight:'10px',height:'100%'}}>
        <div className="commen-flex">
            <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>{title}</span></p>
            <Select
              showSearch
              value={userName}
              placeholder={"输入用户名称搜索"}
              filterOption={false}
              onSearch={debounceFetcher}
              onChange={onChange}
              style={{ width: '250px' }}
             
            
              
            >
              {options.map((item) => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
        </div>
      
        <div>
           
            <div className="content-text">
              <Table columns={columns} dataSource={commitInfo} 
              pagination={{
                pageSize: 5,
                
               
              }} size="small"
              />
            </div>             
        </div>
    </Card>
    </div>
  );
};

export default platTableCard;
