import React, { useEffect, useRef, useState } from "react";
import {Badge ,Avatar,Button ,Table,message,Col, Row,Select,Space,Card  } from 'antd';

const MiddleTextCard = ({info}) => {
  return (
    <div style={{marginRight:'10px',backgroundColor:'#F7F8FB'}} className="middle-card-box middle-card-box-middle">
    <Card style={{ width: '100%' ,marginRight:'10px',height:'200px',backgroundColor:'#F7F8FB'}}>
        <p className="platformBoard-content-title middle-content-title">
          <span style={{color:'#272D35',fontSize:'16px'}}>{info.name}</span>
          <span> 
           <div style={{width:'12px',height:'12px',borderRadius:'3px',backgroundColor:'#0A91B1'}}></div>
          </span>
        </p>
        <div className="middle-content-left">
            <div >
                <p className="middle-content-left-num">{info.count}</p>
                <p style={{color:'#272D35'}}>个实例</p>
                <div style={{marginTop:'10px',color:'#272D35'}}>
                  <p>CPU：{info.cpu}</p>
                  <p>存储：{info.limit}</p>
                </div>
            </div>
            <div className="content-text">
              <p style={{color:'#272D35'}}>内存：{info.disk}</p>
            </div>             
        </div>
    </Card>
    </div>
  );
};

export default MiddleTextCard;
