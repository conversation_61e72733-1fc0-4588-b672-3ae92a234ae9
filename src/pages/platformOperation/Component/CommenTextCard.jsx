import React, { useEffect, useRef, useState } from "react";
import {Badge ,Input,Button ,Table,message,Col, Row,Select,Space,Card  } from 'antd';
import {
    DisconnectOutlined
  } from '@ant-design/icons';
  import  platTitle from '@/assets/images/platTitle.png';
  import  doc1 from '@/assets/images/doc1.png';
  import  doc2 from '@/assets/images/doc2.png';
  import  doc3 from '@/assets/images/doc3.png';
const CommenTextCard = ({info,title,type}) => {
  return (
    <div style={{width:'25%',marginRight:'10px'}}>
    <Card style={{ width: '100%' ,marginRight:'10px',minHeight:'300px'}}>
        <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>{title}</span></p>
        <div className="content-main">
            <div className="content-total">
            {
              info.total?info.total:0
            }
            </div>
            <div className="content-text">
            {
              type === 'app' && info?.data?.length > 0 ?
              info.data.map((item)=>(
                <p className="content-text-contain">
                <span>{item.scaffoldTemplateName}</span>
                <span>{item.count}</span>
                </p>
                )):(
                  type === 'application'? info.data?.map((item)=>(
                    <p className="content-text-contain">
                    
                      {
                        item.stageStatusName === '建设中'?<Space><Badge color="#2FB7F5"/> 建设中</Space>:
                        item.stageStatusName === '规划中'?<Space><Badge color="#1BBE6B"/> 规划中</Space>:
                        item.stageStatusName === '发布审核中'?<Space><Badge color="#FF6067"/>  发布审核中</Space>:
                        item.stageStatusName === '下线审核中'?<Space><Badge color="#FF9900"/>下线审核中</Space>:
                        <Space><Badge status="default" />未知状态</Space>
                        
                      }
                    <span>{item.num}</span>
                    </p>
                    )):
                    (
                      type === 'doc'?
                      <p>
                        <p className="commen-flex-p"><Space><span><img src={doc1} />Api文档</span><span>统计中</span></Space></p>
                        <p className="commen-flex-p"><Space><span><img src={doc2} />建设文档</span><span>统计中</span></Space></p>
                        <p className="commen-flex-p"> <Space><span><img src={doc3} />操作手册</span><span>统计中</span></Space></p>
                       
                      </p>
                      :  (
                        type === 'api'?
                        <p >
                          <p className="commen-flex-p" style={{marginBottom:'30px'}}><Space><span>API访问累计次数</span><span>统计中</span></Space></p>
                          <p className="commen-flex-p"><Space><span>已调用API数</span><span>统计中</span></Space></p>
                        </p>
                        :''
                      )
                    )
                )
            }
           
            
            </div>             
        </div>
    </Card>
    </div>
  );
};

export default CommenTextCard;
