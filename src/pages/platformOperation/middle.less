.middleBoard{
    .middle-header{
        background-color: #fff;
        margin-bottom: 5px;
        border: 1px dotted #ccc;
        padding: 8px;
        border-radius: 3px;
    }
    .header-item{
        width: 15%;
        border-right: 1px solid #ccc;
        text-align: center;
        .header-item-title{
            margin-top: 5px;
            margin-bottom: 8px;
        }
    }
    .segmented-with-gap .ant-segmented-item {
        margin: 0 1.5px !important;
      }
    .platformBoard-content-box{
        background-color: #F7F8FB;
    }
    .platformBoard-content {
        background-color: #fff;
        padding: 10px 8px;
        padding-right: 0px;
    }
    .middle-content-title{
        justify-content: space-between;
    }
    .middle-content-left{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 25px;
    }
    .middle-content-left-num{
        color: #119FA8;
        font-size: 20px;
        font-weight: bold;

    }  
    .middle-card-box{
        display: inline-block;
    } 
 .middle-card-box-middle{
    width: calc(100%/3 - 10px);
    margin-bottom: 10px;
 }
}