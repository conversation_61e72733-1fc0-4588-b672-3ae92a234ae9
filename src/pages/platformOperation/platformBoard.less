.platformBoard{
    padding: 16px;
    .platformBoard-content-title{
        font-size: 16px;
        color: #10173F;
        display: flex;
        align-items: center;
    }
    .content-main{
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        align-items: center;
        height: 200px;
        .content-total{
            font-size: 36px;
        }
        .content-text{
            color: #646566;
            width: 60%;
            .content-text-contain{
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
            }
        }
    }
    .commen-flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .platformBoard-content-box{
        width: calc(49% + 10px);
        margin-right: 10px;
        height: 400px;
    }
    .commen-flex-p{
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
  }
   
}
:global {
    .ant-statistic-title {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      color: #1f1f1f;
      font-size: 24px;
      font-weight: bold;
    }
  }
.border-bottom-only.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector {
    border: none !important;
    border-bottom: 1px solid #d9d9d9 !important;
    border-radius: 0 !important;
    background-color: transparent !important;
  }
  
  .border-bottom-only.ant-select:hover .ant-select-selector {
    border-color: #40a9ff !important;
    box-shadow: none !important;
  }
  
  .border-bottom-only.ant-select-focused .ant-select-selector {
    border-color: #40a9ff !important;
    box-shadow: none !important;
  }