import React, { useEffect, useRef, useState } from "react";
import {DatePicker ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Dropdown, Menu  } from 'antd';
const { RangePicker } = DatePicker;
import { scaffoldTemplate,techStackRank,docCount,apiCount ,statisticApp,statisticMiddle,getUserList,getUserSysCommit} from '@/services';
import * as echarts from 'echarts';
import './platformBoard.less';
import CommenTextCard from './Component/CommenTextCard';
import PlatTableCard from './Component/PlatTableCard'
import  platTitle from '@/assets/images/platTitle.png';
import Code from './Component/LineCard';
import  codeImg from '@/assets/images/code.png';
const dateFormat = 'YYYY-MM-DD';
import { useModel } from 'umi';

const MiddlePlatformBoard = () => {
  const [total, setTotal] = useState();
  const [data, setData] = useState([]);
  const [techData, setTechData] = useState({});
  const [tech, setTech] = useState("前后端");
  const [createTime, setCreateTime] = useState([]);
  const [date, setDate] = useState('全部');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [info, setInfo] = useState({});
  const [docinfo, setDocInfo] = useState({});
  const [apiInfo, setApiInfo] = useState({});
  const [appInfo, setAppInfo] = useState({});
  
  return (
    <div className="middleplatformBoard">
      <div className="platformBoard-header">
       111111
      </div>
        <div className="platformBoard-content ">
        <div style={{display:'flex',marginBottom:'10px'}}>
        
        </div>
        <div style={{display:'flex',marginBottom:'10px'}}>
            
        </div>
        <div style={{height:'350px',display:'flex',alignItems:'center'}}>
            
        </div>
        </div>
    </div>
  );
};


export default MiddlePlatformBoard;




