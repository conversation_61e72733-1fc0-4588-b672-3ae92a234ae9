import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography, Statistic, Input, DatePicker, Tabs, List, Pagination } from 'antd';
import "./index.less";
import Icon from '@ant-design/icons';
const { TabPane } = Tabs;
const { Title } = Typography;
import iconSvg from '../../../assets/images/workbench.svg'
import iconSvgGary from '../../../assets/images/workbrenchGary.svg'

const { RangePicker } = DatePicker;
const { Search } = Input;

const fetchData = async (tabKey, page, pageSize) => {
    // 这里可以替换为实际的 API 请求
    return new Promise((resolve) => {
        setTimeout(() => {
            const mockData = [];
            for (let i = 0; i < pageSize; i++) {
                mockData.push({
                    name: `项名称 ${(page - 1) * pageSize + i + 1}`,
                    initiator: `发起人 ${(page - 1) * pageSize + i + 1}`,
                    receiveTime: '2025-11-14 11:24:14',
                    endDate: '2024-11-14',
                    status: 'story',
                    step: '待评审',
                    stayTime: '4天8小时15分钟',
                    level: '高',
                    desc: '#4396 release->master'
                });
            }
            resolve({
                data: mockData,
                total: 100 // 模拟总数据量
            });
        }, 500);
    });
};

const JobList = (props) => {
    const { userInfo } = props; 
    const [searchValue, setSearchValue] = useState('');
    const [dateRange, setDateRange] = useState([]);
    const [activeTab, setActiveTab] = useState('PaaS');
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [listData, setListData] = useState([]);
    const [total, setTotal] = useState(0);
    const [tabList, setTabList] = useState([
        'PaaS', '存储', '文档', 'API', '需求', '任务', '缺陷', 'TIS工单', '代码合并请求'
    ]);
    const [sortedTabs, setSortedTabs] = useState([]);

    const handleSearch = (value) => {
        // 处理搜索逻辑
        console.log('搜索值:', value);
    };

    const handleDateChange = (dates, dateStrings) => {
        // 处理日期范围选择逻辑
        setDateRange(dates);
        console.log('选择的日期范围:', dateStrings);
    };

    const handleMoreClick = () => {
        debugger
    }

    const handleTabChange = async (key) => {
        setActiveTab(key);
        setCurrentPage(1);
        const { data, total } = await fetchData(key, 1, pageSize);
        setListData(data);
        setTotal(total);
    };

    const handlePageChange = async (page) => {
        setCurrentPage(page);
        const { data, total } = await fetchData(activeTab, page, pageSize);
        setListData(data);
        setTotal(total);
    };

    useEffect(() => {
        const fetchInitialData = async () => {
            const { data, total } = await fetchData(activeTab, currentPage, pageSize);
            setListData(data);
            setTotal(total);
        };
        fetchInitialData();
    }, []);
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Tab') {
                event.preventDefault(); // 阻止默认的 Tab 键行为
                const currentIndex = tabList.indexOf(activeTab);
                const nextIndex = (currentIndex + 1) % tabList.length;
                setActiveTab(tabList[nextIndex]);
            }
        };

        window.addEventListener('keydown', handleKeyDown);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [activeTab]);

    const paasTab = (
        <TabPane tab="PaaS" key="PaaS">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => (
                        <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                            <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                {/* 使用 img 标签显示本地 SVG 图片 */}
                                <img
                                    src={iconSvg}
                                    alt=""
                                    style={{ width: 18, height: 18, marginRight: 10 }}
                                />
                                {/* 名称 */}
                                <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                    {item.name}
                                </div>
                            </div>
                            {/* 发起人及接收时间 */}
                            <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                <span>发起人：{item.initiator}</span>
                                <span style={{marginLeft: '100px'}}>接收时间： {item.receiveTime}</span>
                            </div>
                        </List.Item>
                    )}
                />
            </div>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />
        </TabPane>
    )
    const storageTab = (
        <TabPane tab="存储" key="存储">
            <div style={{height: '410px', overflow: 'auto'}}></div>    
        </TabPane>
    )
    const documentTab = (
        <TabPane tab="文档" key="文档">
            <div style={{height: '410px', overflow: 'auto'}}></div>    
        </TabPane>
    )
    const apiTab = (
        <TabPane tab="API" key="API">
            <div style={{height: '410px', overflow: 'auto'}}></div>    
        </TabPane>
    )
    const demandTab = (
        <TabPane tab="需求" key="需求">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.status) {
                        case 'feature':
                            backgroundColor = '#008DDC';
                            break;
                        case 'story':
                            backgroundColor = '#0EC804';
                            break;
                        case 'Epic':
                            backgroundColor = '#042B41';
                            break;
                        default:
                            backgroundColor = 'transparent';
                        }
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    {/* 使用 img 标签显示本地 SVG 图片 */}
                                    <span style={{ backgroundColor, color: 'white', padding: '0px 5px 2px 5px',lineHeight: '16px', borderRadius: '3px', marginRight: '10px' }}>
                                        aaa
                                    </span>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.name}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span style={{display: 'inline-block', width: '14%'}}>状态：<span style={{color: '#1890ff'}}>{item.step}</span></span>
                                    <span style={{display: 'inline-block', width: '26%'}}>预计结束时间： {item.endDate}</span>
                                    <span style={{display: 'inline-block', width: '30%', whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>停留时间： {item.stayTime}</span>
                                    <span style={{display: 'inline-block', width: '30%', whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>所属项目： {item.endDate}</span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />
        </TabPane>
    )
    const taskTab = (
        <TabPane tab="任务" key="任务">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    {/* 使用 img 标签显示本地 SVG 图片 */}
                                    <span style={{ backgroundColor: '#008ddc', color: 'white', padding: '0px 5px 2px 5px',lineHeight: '16px', borderRadius: '3px', marginRight: '10px' }}>
                                        task
                                    </span>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.name}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span style={{display: 'inline-block', width: '14%'}}>状态：<span style={{color: '#1890ff'}}>{item.step}</span></span>
                                    <span style={{display: 'inline-block', width: '26%'}}>预计结束时间： {item.endDate}</span>
                                    <span style={{display: 'inline-block', width: '30%', whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>停留时间： {item.stayTime}</span>
                                    <span style={{display: 'inline-block', width: '30%', whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>所属项目： {item.endDate}</span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />     
        </TabPane>
    )
    const defectTab = (
        <TabPane tab="缺陷" key="缺陷">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.status) {
                        case 'bug':
                            backgroundColor = '#fa1937';
                            break;
                        default:
                            backgroundColor = 'transparent';
                        }
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    {/* 使用 img 标签显示本地 SVG 图片 */}
                                    <span style={{ backgroundColor, color: 'white', padding: '0px 5px 2px 5px',lineHeight: '16px', borderRadius: '3px', marginRight: '10px' }}>
                                        bug
                                    </span>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.name}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span style={{display: 'inline-block', width: '14%'}}>状态：<span style={{color: '#1890ff'}}>{item.step}</span></span>
                                    <span style={{display: 'inline-block', width: '26%'}}>预计结束时间： {item.endDate}</span>
                                    <span style={{display: 'inline-block', width: '30%', whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>停留时间： {item.stayTime}</span>
                                    <span style={{display: 'inline-block', width: '30%', whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>所属项目： {item.endDate}</span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            /> 
        </TabPane>
    )
    const tisTab = (
        <TabPane tab="TIS工单" key="TIS工单">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.level) {
                        case '高':
                            backgroundColor = '#fa1937';
                            break;
                        case '中':
                            backgroundColor = '#6997ff';
                            break;
                        case '低':
                            backgroundColor = '#8797aa';
                            break;
                        default:
                            backgroundColor = 'transparent';
                        }
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.name}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span style={{display: 'inline-block', float: 'left', width: '16%'}}>工单状态：<span style={{color: '#1890ff'}}>{item.step}</span></span>
                                    <span style={{display: 'inline-block', float: 'left', width: '12%'}}>
                                        优先级：<span style={{backgroundColor, color: '#fff', padding: '2px 5px', borderRadius: '3px', fontSize: '12px'}}>{item.level}</span>
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '22%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        发起人：<span style={{color: '#444'}}>{item.stayTime}</span>
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '25%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        创建时间：<span style={{color: '#444'}}>{item.receiveTime}</span>
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '25%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        流程名称：<span style={{color: '#444'}}>{item.endDate}</span>
                                    </span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />  
        </TabPane>
    )
    const codeTab = (
        <TabPane tab="代码合并请求" key="代码合并请求">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.level) {
                        case '高':
                            backgroundColor = '#fa1937';
                            break;
                        case '中':
                            backgroundColor = '#6997ff';
                            break;
                        case '低':
                            backgroundColor = '#8797aa';
                            break;
                        default:
                            backgroundColor = 'transparent';
                        }
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.name}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span style={{display: 'inline-block', float: 'left', width: '16%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        {item.desc}
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '12%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        状态：
                                        <span style={{color: '#1890ff'}}>{item.step}</span>
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '18%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        发起人：<span style={{color: '#444'}}>{item.stayTime}</span>
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '18%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        发起时间：<span style={{color: '#444'}}>{item.receiveTime}</span>
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '18%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        应用系统：<span style={{color: '#444'}}>{item.endDate}</span>
                                    </span>
                                    <span style={{display: 'inline-block', float: 'left', width: '18%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>
                                        应用程序：<span style={{color: '#444'}}>{item.endDate}</span>
                                    </span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />   
        </TabPane>
    )
    const tabMap = {
        'PaaS': paasTab,
        '存储': storageTab,
        '文档': documentTab,
        'API': apiTab,
        '需求': demandTab,
        '任务': taskTab,
        '缺陷': defectTab,
        'TIS工单': tisTab,
        '代码合并请求': codeTab,
    };
    useEffect(() => {
        const fetchTabList = async () => {
            // 获取tab排序顺序 并存储到tabList 
            const response = ['PaaS', '存储', '文档', 'API', '需求', '任务', '缺陷', '代码合并请求', 'TIS工单', ];
            setTabList(response);
            setActiveTab(response[0]); 
        };
        fetchTabList();
    }, []);
    useEffect(() => {
        const sorted = tabList.map(key => tabMap[key]);
        setSortedTabs(sorted);
    }, [tabList, listData])
    return (
        <div className='jobList' style={{height:'580px',overflow:'auto', background: '#fff', padding: 10}}>
            <div style={{ display: 'flex', alignItems: 'center', padding: '0px 10px', justifyContent: 'space-between', lineHeight: '32px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{ backgroundColor: '#008bd6', width: 6, height: 20, marginRight: 8, borderRadius: 3 }} />
                    <Typography.Title level={5} style={{ margin: 0, fontWeight: 500 }}>工作事项</Typography.Title>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Search
                        placeholder="请输入搜索内容"
                        onSearch={handleSearch}
                        value={searchValue}
                        onChange={(e) => setSearchValue(e.target.value)}
                        style={{ width: 200, marginRight: 16 }}
                    />
                    <RangePicker
                        onChange={handleDateChange}
                        value={dateRange}
                        style={{ width: 200 }}
                    />
                </div>
            </div>
            <div className='list-content'>
                <div>
                    <Tabs activeKey={activeTab} onChange={handleTabChange} style={{height: '56px', marginTop: '5px'}}>
                        {sortedTabs}
                    </Tabs>
                </div>
            </div>
        </div>
    );
};

export default JobList;
