import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography, Table, List, message } from 'antd';
import { ToolOutlined, RiseOutlined, FileWordOutlined, DatabaseOutlined } from '@ant-design/icons';
import "./index.less";


const Count = (props) => {
    const { statModule } = props

    const renderContent = (icon, title, customColor, data) => {
        return (
            <div style={{height: '100%'}}>
                <div style={{display: 'flex', alignItems: 'center', padding: '10px 0'}}>
                    {icon}
                    <Typography.Title level={5} style={{ margin: 0, fontWeight: 500, marginLeft: 8 }}>
                        {title}
                    </Typography.Title>
                </div>
                <List
                    dataSource={data}
                    renderItem={(item) => (
                        <List.Item>
                            <div className="rectangle" style={{backgroundColor: customColor}} />
                            <Typography.Text className='text-style'>{item}</Typography.Text>
                        </List.Item>
                    )}
                />
            </div>
        );
    };

    return (
        <div className='countList collectList' style={{ height: '580px', overflow: 'auto', background: '#fff', padding: 10 }}>
            <Row gutter={16}>
                <Col span={12}>
                    <div style={{ height: '270px' }}>
                        {renderContent(<DatabaseOutlined style={{fontSize: '16px'}} />, 'PaaS 服务申请 top5', '', statModule['PaaS服务申请']?.slice(0, 5))}
                    </div>
                </Col>
                <Col span={12}>
                    <div style={{ height: '270px' }}>
                        {renderContent(<RiseOutlined style={{fontSize: '16px'}} /> , 'API 订阅 top5', '#008A56', statModule['API订阅'])}
                    </div>
                </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={12}>
                    <div style={{ height: '270px' }}>
                        {renderContent(<FileWordOutlined style={{fontSize: '16px'}} />, '文档阅读 top5', '#E7000B', statModule['文档阅读'])}
                    </div>
                </Col>
                <Col span={12}>
                    <div style={{ height: '270px' }}>
                        {renderContent(<ToolOutlined style={{fontSize: '16px'}} />, '技术组件访问 top5', '#E09B00', statModule['技术组件访问'])}
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default Count;
