.workbench {
    .work-left {
        width: 248px;
        height: 910px;
        background: #fff;
    }
    .nav {
        height: 920px;
        .notice-content {
            .micro-list {
                border: none;
                padding: 5px 15px;
                .micro-list-item {
                    padding: 10px 0;
                }
            }
        }
        .quick-content {
            margin-top: 40px;
        }
    }
    .work-content {
        flex: 1;
        padding-left: 8px;
        box-sizing: border-box;
        .list-content {
            padding: 0 15px;
            .micro-list {
                .micro-list-item {
                    padding: 8px 0;
                }
            }
        }
        .systemList {
            tr {
                th,td {
                    border: none;
                }
                th {
                    font-weight: normal;
                }
                th::before {
                    display: none;
                }
            }
            .system-content {
                .micro-list-item {
                    padding-left: 15px;
                }
            }
        }
        .countList {
            .rectangle {
                display: inline-block;
                background-color: #008bd6;
                width: 6px;
                height: 20px;
                margin-right: 8px;
                border-radius: 3px;
            }
            .micro-list {
                .micro-list-item {
                    padding: 12px 10px;
                    justify-content: left;
                    border: none;
                }
                .text-style {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}