import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography } from 'antd';
import "./index.less";
import { history } from 'umi';
import Nav from './nav';
import JobList from './jobList';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import Collect from './collect';
import System from './system';
import App from './app';
import Count from './count';
import { SettingOutlined } from '@ant-design/icons';
import { getWorkbenchData, getWorkbenchInfo } from '@/services/workbench';


const MyWorkbench = () => {
    const [userInfo, setUserInfo] = useState({
        userId: '',
        admin: false
    })
    const [accessConfigs, setAccessConfigs] = useState([]) // 快捷入口
    const [appSystems, setAppSystems] = useState([])    // 最近使用系统
    const [appPrograms, setAppPrograms] = useState([])      // 最近使用程序
    const [documentBrowses, setDocumentBrowses] = useState([])      // 文档浏览记录
    const [statModule, setStatModule] = useState({})        // 统计模块区域
    const [systemNum, setSystemNum] = useState(0)
    const [programNum, setProgramNum] = useState(0)

    useEffect(() => {
        const fetchUserInfo = async () => {
            const resultRes = await getWorkbenchInfo();
            const resData = {
                userId: resultRes.id,
                admin: resultRes.admin,
            }
            setUserInfo(resData)
        };
        fetchUserInfo();
    },[])

    useEffect(() => {
        const fetchDataInfo = async () => {
            getWorkbenchData().then(res => {
                if (res.systemNum) {
                    setSystemNum(res.systemNum)
                }
                if (res.programNum) {
                    setProgramNum(res.programNum)
                }
                if (res.accessConfigs && res.accessConfigs.length > 0) {
                    setAccessConfigs(res.accessConfigs)
                }
                if (res.appSystems && res.appSystems.length > 0) {
                    setAppSystems(res.appSystems)
                }
                if (res.appPrograms && res.appPrograms.length > 0) {
                    setAppPrograms(res.appPrograms)
                }
                if (res.documentBrowses && res.documentBrowses.length > 0) {
                    setDocumentBrowses(res.documentBrowses)
                }
                if (res.statModule) {
                    setStatModule(res.statModule)
                }
            })
        };
        fetchDataInfo();
    },[])
    
    const linkList = [
        {
            title: '我的工作台'
        },
    ]

    const handleSetEvent = () => {
        history.push(`/myWorkbench/set`);
    }

    return (
        <div className='workbench' style={{height:'100%',overflow:'auto', display: 'flex'}}>
            <div className='work-left'>
                <Nav accessConfigs={accessConfigs} systemNum={systemNum} programNum={programNum}></Nav>
            </div>
            <div className="work-content">
                <div style={{ display: 'flex', alignItems: 'center', background: '#fff' }}>
                    <BreadcrumbNav list={linkList} />
                    <SettingOutlined
                        style={{ marginRight: '30px', fontSize: '20px', color: '#1890ff', cursor: 'pointer' }}
                        onClick={handleSetEvent}
                    />
                </div>
                <Row gutter={16} style={{marginTop: '10px',width: '100%', overflowX: 'hidden'}}>
                    <Col span={16}>
                        <JobList userInfo={userInfo}></JobList>
                    </Col>
                    <Col span={8}>
                        {userInfo.admin ? <Count statModule={statModule} /> : <Collect documentBrowses={documentBrowses} />}
                    </Col>
                </Row>
                <Row gutter={16} style={{marginTop: '10px',width: '100%', overflowX: 'hidden'}}>
                    <Col span={12}>
                        <System appSystems={appSystems}></System>
                    </Col>
                    <Col span={12}>
                        <App appPrograms={appPrograms}></App>
                    </Col>
                </Row>
            </div>
        </div>
    );
};

export default MyWorkbench;
