import React, { useEffect, useRef, useState } from 'react';
import { Card, Row, Col, Typography, Statistic, Button, Space, message } from 'antd';
import "./index.less";
import { history } from 'umi';
import { SaveOutlined } from '@ant-design/icons';
import LeftSet from './leftSet';
import RightSet from './rightSet';
import { getWorkbenchConfig, saveWorkbenchConfig } from '@/services/workbench';

const { Title } = Typography;

const Set = () => {

    const rightRef = useRef(null);
    const [configData, setConfigData] = useState({
        moduleShows: [],
        workItems: [],
        accessConfigs: [],
        userId: '',
    })

    useEffect(() => {
        const fetchSetConfig = async () => {
            getWorkbenchConfig().then(res => {
                const resData = {
                    moduleShows: res.moduleShows,
                    workItems: res.workItems,
                    accessConfigs: res.accessConfigs,
                    userId: res.userId
                }
                setConfigData(resData)
            })
            
        };
        fetchSetConfig();
    },[])

    // 这里可以添加处理取消和保存操作的函数
    const handleCancel = () => {
        history.push(`/myWorkbench`);
    };

    const handleSave = async () => {
        const { error, values } = await rightRef.current.validateForm();

        if (error) {
            message.error('请输入合法的表单字符');
            return;
        }
        const query = {
            userId: configData.userId,
            moduleShows: configData.moduleShows,
            workItems: configData.workItems,
            accessConfigs: configData.accessConfigs,
        }
        saveWorkbenchConfig(query).then(res => {
            if (res) {
                message.success('保存成功')
                history.push(`/myWorkbench`);
            } else {
                message.error('保存失败')
            }
            
        })
    }; 
    
    const handleModuleShowsChange = (newModuleShows) => {
        setConfigData((prevParams: any) => ({
            ...prevParams,
            moduleShows: newModuleShows,
        }));
    };

    const handleWorkItemsChange = (newWorkItems) => {
        setConfigData((prevParams: any) => ({
            ...prevParams,
            workItems: newWorkItems,
        }));
    };

    const handleAccessConfigChange = (newAccessConfigs) => {
        setConfigData((prevParams: any) => ({
            ...prevParams,
            accessConfigs: newAccessConfigs,
        }));
    };

    return (
        <div className='set'>
            <Row justify="space-between" align="middle" style={{ padding: '25px 30px' }}>
                <Col>
                    <Title style={{textIndent: '20px'}} level={3}>工作台设置</Title>
                </Col>
                <Col>
                    <Space>
                        <Button onClick={handleCancel}>取消</Button>
                        <Button type="primary" onClick={handleSave}>
                            <SaveOutlined />
                            保存
                        </Button>
                    </Space>
                </Col>
            </Row>
            <div className='main-content'>
                <Row gutter={[16, 16]} style={{height: '100%'}}>
                    {/* 左侧区域，占据 16 份栅格 */}
                    <Col span={16} style={{height: '100%'}}>
                        <LeftSet moduleShows={configData.moduleShows} onModuleShowsChange={handleModuleShowsChange} workItems={configData.workItems} onWorkItemsChange={handleWorkItemsChange}></LeftSet>
                    </Col>
                    {/* 右侧区域，占据 8 份栅格 */}
                    <Col span={8} style={{height: '100%'}}>
                        <RightSet ref={rightRef} accessConfigs={configData.accessConfigs} onAccessConfigChange={handleAccessConfigChange}></RightSet>
                    </Col>
                </Row>
            </div>
        </div>
    );
};

export default Set;
