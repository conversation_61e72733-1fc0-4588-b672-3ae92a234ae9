import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { Tabs } from 'antd';

import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import './list.less';
import Component from './component.jsx'
import Application from './application.jsx'



const OpenSourceComponentUse = () => {

    const linkList = [
        {
          title: '开源组件管理'
        }
      ]
      const onChange = (key) => {
        console.log(key);
      };

  return (
    <div className="openSourceComponent openSourceManage">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} />
      <div  className='micro-spin-container-box sub-app-box-container-content height-p100' style={{margin:'8px'}}>
       
        <Tabs className='height-p100' defaultActiveKey="1" items={[
            {
              key: '1',
              label: '基线维度',
              children:<div style={{height:'100%'}}><Component /></div> ,
            },
            {
              key: '2',
              label: '应用系统维度',
              children: <div style={{height:'100%'}}><Application /></div>,
            }]} 
            onChange={onChange} />
      </div>
   
    </div>
  );
};



export default OpenSourceComponentUse;