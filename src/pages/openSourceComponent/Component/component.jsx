import React, { useState, useEffect } from 'react';
import {
  Table,
  Input,
  Card,
  Button,
  Row,
  Col,
  Typography,
  Space,
  Tag,
  List,
  message
} from 'antd';
import { SearchOutlined,CodeOutlined ,CloudServerOutlined ,ApiOutlined } from '@ant-design/icons';
import { getOpensourceList,getComponentDimension} from '@/services';
import { dataSource } from '@/pages/passService/dataDesensitization/type';
import axios from 'axios';
import { BaseUrlEnum } from '@/enums/httpEnum'
const { Title } = Typography;
import Local from "@/utils/storage";
const { Search } = Input;
const OpenSourceComponentSystem = () => {
  // 模拟数据
  const [data, setData] = useState([]);
  const [listData, setListData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [clickName, setClickName] = useState('');
  const [systemCount, setSystemCount] = useState(0);
  const [appCount, setAppCount] = useState(0);

  //获取分类
const getOpensourceListData = () => {
  getOpensourceList().then((res) => {
    if(res){
      setListData(res)
      if(res.length > 0){
        setClickName(res[0].name)
        getItemCom(res[0])
      }
    }else{
      message.error(res?.message || res?.msg)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
const getItemCom = (item) => {
  setClickName(item.name)
  getComponentDimension(item.name).then((res) => {
    if(res){
      
      setData(res)
    }else{
      message.error(res?.message || res?.msg)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
useEffect(() => {
  if(data.length > 0){
    const uniqueSystems = new Set(data.map(item => item.applicationSystemName));
    const uniqueApps = new Set(data.map(item => item.applicationName));
    setSystemCount(uniqueSystems.size)
    setAppCount(uniqueApps.size)
  }else{
    setSystemCount(0)
    setAppCount(0)
  }
 
}, [data]);
  useEffect(() => {
    getOpensourceListData()
  }, []);
  // 处理搜索
  useEffect(() => {
    if (searchText) {
      const filtered = listData.filter(item =>
        {
          return item.name.toLowerCase().includes(searchText.toLowerCase())
        })
       
      setFilteredData(filtered);
      if(filtered.length > 0){
        setClickName(filtered[0].name)
        getItemCom(filtered[0])
      }
    } else {
      setFilteredData([...listData]);
      if(listData.length > 0){
        setClickName(listData[0].name)
        getItemCom(listData[0])
      }
    }
  }, [searchText, listData]);

  // 表格列定义
  const columns = [
    {
      title: '应用系统名称',
      dataIndex: 'applicationSystemName',
      key: 'applicationSystemName',
      render: (v) => <div className='text-overflow' style={{width:'150px'}} title={v}>{v}</div>,
    },
    {
      title: '应用系统管理员',
      dataIndex: 'systemManager',
      key: 'systemManager',
      render: (v) => <div className='text-overflow' style={{width:'100px'}} title={v}>{v}</div>
    },
    {
      title: '应用程序名称',
      dataIndex: 'applicationName',
      key: 'applicationName',
      render: (v) => <div className='text-overflow' style={{width:'120px'}} title={v}>{v}</div>,
    },
    {
      title: '应用程序开发负责人',
      dataIndex: 'developmentLeader',
      key: 'developmentLeader',
      render: (v) => <div className='text-overflow' style={{width:'100px'}} title={v}>{v}</div>,
    },
    {
      title: '使用版本',
      dataIndex: 'usedVersion',
      key: 'usedVersion',
      render: (version) => {
       return version?<Tag color="blue">{version}</Tag>:''
      }
    },
    {
      title: '登记时间',
      dataIndex: 'registrationTime',
      key: 'registrationTime'
    }
  ];
    // const downloadResponse = async () => {
    //   try {
    //     // 调用后端导出接口
    //     const response = await fetch(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/use/component-dimension/export?name=${clickName}`, {
    //       method: 'GET',
    //       headers: {
    //         'Content-Type': 'application/json',
    //         'Authorization': `Bearer ${Local.getLocal(TOKEN_ENUM)}`,
    //       }
    //     });
        
    //     if (!response.ok) {
    //       throw new Error(`导出失败: ${response.statusText}`);
    //     }
        
    //     // 将响应转换为Blob对象
    //     const blob = await response.blob();
        
    //     // 创建Blob URL
    //     const url = window.URL.createObjectURL(blob);
        
    //     // 创建临时链接元素并触发下载
    //     const a = document.createElement('a');
    //     a.style.display = 'none';
    //     a.href = url;
    //     a.download = '导出数据.xlsx'; // 设置文件名
        
    //     // 添加到DOM中（某些浏览器需要）
    //     document.body.appendChild(a);
        
    //     // 模拟点击下载
    //     a.click();
        
    //     // 清理URL对象
    //     window.URL.revokeObjectURL(url);
        
    //     // 移除临时链接元素
    //     document.body.removeChild(a);
    //   } catch (error) {
    //     console.error('导出过程中发生错误:', error);
    //     alert('导出失败，请重试或联系管理员');
    //   }
    // };

  const downloadResponse = async () => {
    if(!clickName){
      message.error('请先选择开源组件')
      return;
    }
    try {
      const response = await axios.get(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/use/component-dimension/export?name=${clickName}`, {
        responseType: 'blob', // 关键配置
        params: {
          // 你的请求参数
        }
      });
  
      // 创建Blob对象
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
  
      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      
      // 尝试从响应头获取文件名
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'app.xlsx';
      
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename="?(.+)"?/);
        if (fileNameMatch && fileNameMatch.length === 2) {
          fileName = fileNameMatch[1];
        }
      }
      
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 释放URL对象
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('下载失败:', error);
    }
  };
  const onSearch= (value) => {
    setSearchText(value)
  }
  return (
    <div style={{backgroundColor: '#f5f5f5',height:'100%',marginTop:'-8px'}}>
        <div style={{display:'flex',height:'100%'}}>
            <div style={{marginRight:'8px',width:'350px'}}>
                <Card style={{height:'100%'}}>
                    <div style={{margin:'-10px'}}>
                        <div style={{ marginBottom: '16px' }}>
                        <div className='flex-app' style={{marginBottom:'25px'}}>
                            <div className='header-icon'></div>
                            <div>开源组件</div>
                        </div>
                        <Search  placeholder="搜索开源组件" onSearch={onSearch} style={{ width: 300 }} allowClear />

                    </div>
                    <List
                    header={<div>开源组件名称</div>}
                        itemLayout="vertical"
                        size="large"
                        dataSource={filteredData}
                        renderItem={(item) => (
                        <List.Item
                            key={item.id}
                            onClick={()=>{getItemCom(item)}}
                            style={{backgroundColor: clickName === item.name?'#EDF3FF':''}}
                        >
                        
                            {item.name}
                        </List.Item>
                        )}
                    />
                   </div>
                </Card>
            </div>
            <div style={{backgroundColor:'#fff',width:'calc(100% - 358px)'}}>
                <Card style={{height:'100%'}}>
                    <div className='flex-app' style={{marginBottom:'25px'}}>
                        <div className='header-icon'></div>
                        <div>使用组件的应用程序</div>
                    </div>
                    <p className='export'>
                            <span><span style={{color:'#1a9fe7'}}>{clickName}</span>在 <span style={{color:'red'}}>{systemCount}</span>个应用系统，<span style={{color:'red'}}>{appCount}</span>个程序中被使用</span>
                            <Button type="primary" onClick={()=>{downloadResponse()}}>导出</Button>
                    </p>
                    <Table
                    columns={columns}
                    dataSource={data}
                    pagination={{
                        ...pagination,
                        total: data.length,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `共${total} 条`,
                        pageSizeOptions: ['10', '20', '50'],
                    }}
                    onChange={(pagination) => setPagination(pagination)}
                    />
                </Card>
            </div>
            </div>
    </div>
  );
};

export default OpenSourceComponentSystem;