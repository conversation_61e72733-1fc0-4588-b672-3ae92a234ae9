import React, { useState, useEffect } from 'react';
import {
  Table,
  Input,
  Card,
  Button,
  message,
  Col,
  Typography,
  Space,
  Tag,
  List
} from 'antd';
const { Search } = Input;
import { SearchOutlined,CodeOutlined ,CloudServerOutlined ,ApiOutlined } from '@ant-design/icons';
import { getAppList,getAppDimension} from '@/services';
import axios from 'axios';
import { BaseUrlEnum } from '@/enums/httpEnum'
const { Title } = Typography;

const OpenSourceComponentApplication = () => {
  // 模拟数据
  const [data, setData] = useState([]);
  const [listDataAll, setListDataAll] = useState([]);
  const [filteredData, setFilteredData] = useState(data);
  const [searchText, setSearchText] = useState('');
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [listData, setListData] = useState([]);
  const [id, setId] = useState('');
  const [clickNmae, setClickNmae] = useState('');
  const [length, setLength] = useState(0);
  
  
  useEffect(() => {
    getAppListData(1,7)
    
  }, []);
//获取分类
const getAppListData = (page,size) => {
getAppList({current:page,size:size}).then((res) => {
  if(res){
    setListData(res.records)
    if(res.records.length > 0){
      setId(res.records[0].id)
      setClickNmae(res.records[0].cnName)
      getItemApp(res.records[0])
    }
  }else{
    message.error(res?.message || res?.msg)
  }
}).catch((data) => {
  message.error(data?.msg)
})
}

const getItemApp = (item) => {
  setClickNmae(item.cnName)
  setId(item.id)
  getAppDimension(item.id).then((res) => {
    if(res){
      setData(res)
      if(res.length > 0){
        const componentNames = res.map(item => item.componentName || '');
        const uniqueLength = new Set(componentNames).size;
        setLength(uniqueLength)
      }else{
        setLength(0)
      }
    
    }else{
      message.error(res?.message || res?.msg)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
const downloadResponse = async () => {
  if(!id){
    message.error('请先选择应用系统')
    return;
  }
  try {
    const response = await axios.get(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/use/app-dimension/export?projectId=${id}`, {
      responseType: 'blob', // 关键配置
      params: {
        // 你的请求参数
      }
    });

    // 创建Blob对象
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    
    // 尝试从响应头获取文件名
    const contentDisposition = response.headers['content-disposition'];
    let fileName = 'export.xlsx';
    
    if (contentDisposition) {
      const fileNameMatch = contentDisposition.match(/filename="?(.+)"?/);
      if (fileNameMatch && fileNameMatch.length === 2) {
        fileName = fileNameMatch[1];
      }
    }
    
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 释放URL对象
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('下载失败:', error);
  }
};
// useEffect(() => {
//   if(data.length > 0){
//     const uniqueSystems = new Set(data.map(item => item.applicationSystemName));
    
//     setAppCount(uniqueApps.size)
//   }
 
// }, [data]);
// const downloadResponse = async () => {
//   if(!id){
//     message.error('请先应用系统')
//     return;
//   }
//   try {
//     const response = await axios.get(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/use/app-dimension/export?projectId =${id}`,{}, {
//       responseType: 'blob', 
//     });
//     const url = window.URL.createObjectURL(new Blob([response.data]));
//     const link = document.createElement('a');
//     link.href = url;
//     link.setAttribute('download', '');
//     document.body.appendChild(link);
//     link.click();
//     // 清理
//     window.URL.revokeObjectURL(url);
//     document.body.removeChild(link);
//   } catch (error) {
//     console.error('Download failed:', error);
//     // 错误处理
//   }
// };

  // 处理搜索
  // 处理搜索
  const getListAll =(searchText)=> {
    if (searchText) {//如果进行搜索就从所有数据中筛选
      getAppList({current:1,size:1000,cnName:searchText}).then((res) => {
        if(res){
          setListData(res.records)
        }else{
          message.error(res?.message || res?.msg)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    } else {
      getAppList({current:1,size:7}).then((res) => {
        if(res){
          setListData(res.records)
        }else{
          message.error(res?.message || res?.msg)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
  }
  useEffect(() => {
    if (searchText) {//如果进行搜索就从所有数据中筛选
      getAppList({current:1,size:1000,cnName:searchText}).then((res) => {
        if(res){
          setListData(res.records)
        }else{
          message.error(res?.message || res?.msg)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    } else {
      getAppList({current:1,size:7}).then((res) => {
        if(res){
          setListData(res.records)
        }else{
          message.error(res?.message || res?.msg)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
  }, [searchText]);

  useEffect(() => {
    if(listData.length > 0){
      setId(listData[0].id)
      setClickNmae(listData[0].cnName)
      getItemApp(listData[0])
    }
  }, [listData]);
  // 表格列定义
  const columns = [
    
    {
      title: '组件名称',
      dataIndex: 'componentName',
      key: 'componentName',
      render: (v) => <div className='text-overflow' style={{width:'120px'}} title={v}>{v}</div>
    },
    {
      title: '应用程序',
      dataIndex: 'application',
      key: 'application',
      render: (v) => <div className='text-overflow' style={{width:'120px'}} title={v}>{v}</div>
    },
    {
      title: '组件分类',
      dataIndex: 'category',
      key: 'category',
      render: (v) => <div className='text-overflow' style={{width:'100px'}} title={v}>{v}</div>
    },
   
    {
      title: '基线版本',
      dataIndex: 'baselineVersion',
      key: 'baselineVersion',
      render: (baselineVersion) => {
        return baselineVersion?<Tag color="gold">{baselineVersion}</Tag>:''
      }
    },
    {
      title: '使用版本',
      dataIndex: 'usedVersion',
      key: 'usedVersion',
      render: (usedVersion) => {
        return usedVersion?<Tag color="gold">{usedVersion}</Tag>:''
      }
    },
    {
      title: '登记时间',
      dataIndex: 'registrationTime',
      key: 'registrationTime'
    }
  ];
 
  const onSearch= (value) => {
    setSearchText(value)
  }

  return (
    
    <div style={{backgroundColor: '#f5f5f5',height:'100%',marginTop:'-8px'}}>
        <div style={{display:'flex',height:'100%'}}>
            <div style={{marginRight:'8px',width:'350px'}}>
                <Card style={{height:'100%',overflow:'auto'}}>
                    <div style={{margin:'-10px'}}>
                        <div className='flex-app' style={{marginBottom:'25px'}}>
                            <div className='header-icon'></div>
                            <div>应用系统</div>
                        </div>
                        <Search allowClear  placeholder="搜索应用系统名称" onSearch={onSearch} style={{ width: 300 }} />
                     
                        
                    </div>
                    
                    <List
                    header={<div>应用系统名称</div>}
                        itemLayout="vertical"
                        size="large"
                        dataSource={listData}
                        renderItem={(item) => (
                        <List.Item
                            key={item.id}
                            onClick={()=>{getItemApp(item)}}
                            style={{backgroundColor: id === item.id?'#EDF3FF':''}}
                        >
                       
                            {item.cnName}
                        </List.Item>
                        )}
                    />
                </Card>
            </div>
            <div style={{width:'calc(100% - 358px)',backgroundColor:'#fff'}}>
                <Card style={{height:'100%'}}>
                    <div className='flex-app' style={{marginBottom:'25px'}}>
                        <div className='header-icon'></div>
                        <div>应用程序使用的开源组件</div>
                    </div>
                    <p className='export'>
                            <span> 应用系统
                            <span style={{color:'#1a9fe7'}}>{clickNmae}</span>
                             共使用了  <span style={{color:'red'}}>{length}</span> 个开源组件</span>
                            <Button type="primary"  onClick={()=>{downloadResponse()}}>导出</Button>
                    </p>
                    <Table
                    columns={columns}
                    dataSource={data}
                    pagination={{
                      showTotal: (total) => `总共 ${total} 条`,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      pageSizeOptions: ['10', '20', '50']
                    }}
                    />
                </Card>
            </div>
            </div>
    </div>
  );
};

export default OpenSourceComponentApplication;