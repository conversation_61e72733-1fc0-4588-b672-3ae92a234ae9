import React, { useState, useEffect } from 'react';
import {
    Table,
    Input,
    Button,
    Space,
    Form,
    message,
    Pagination,
    Modal,
    Badge,
    Row ,
    Col,
    Select
} from 'antd';
import { useParams, history,useLocation } from 'umi';
import {

    PlusOutlined,

} from '@ant-design/icons';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
const { Search } = Input;
import axios from 'axios';
import './list.less';
import { getApp, getReviewAppList, updateReview } from '@/services';
import { BaseUrlEnum } from '@/enums/httpEnum'
const { Option } = Select;
const { confirm } = Modal;
const AddReviewList = () => {
    const [dataSource, setDataSource] = useState([]);
    const [dataSourceOrigin, setDataSourceOrigin] = useState([]);
    const location = useParams();
    const locationState = useLocation();
    const { appName} = locationState.state || {}
    
    const { taskId } = location || {};
    const [name, setName] = useState();
    const [application, setApplication] = useState([]);
    const [isEdit, setIsEdit] = useState(false);
    const [editingKey, setEditingKey] = useState('');
    const [form] = Form.useForm();
    const [stats, setStats] = useState({
        highLevel: 0,
        mediumLevel: 0,
        lowLevel: 0,
        noLevel: 0,
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
   
    console.log(taskId);
    const [applicationId, setApplicationId] = useState();

    const [loading, setLoading] = useState(false);
    //获取所有应用系统
    const getApplication = () => {
        getApp().then((res) => {
            if (res.length > 0) {
                setApplicationId(res[0].id);
            }
            setApplication(res);
            
        });
    };
    
    useEffect(() => {
        if(!taskId){
            return;
        }
        if(taskId === 'add'){
            getApplication();
        }else {
            setApplication([{
                cnName:appName,
                id:taskId
            }])
            setApplicationId(taskId)
        }
       
    }, [taskId]);
    // useEffect(() => {
    //    if(taskId && taskId !== 'add'){
    //     setApplicationId(taskId)
    //    }
    // }, [taskId]);
    useEffect(() => {
        if (!applicationId) {
            return;
        }
        getData(applicationId);
    }, [applicationId]);
    useEffect(() => {
        const newStats = {
            highLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '高风险',
            ).length,
            mediumLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '中风险',
            ).length,
            lowLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '低风险',
            ).length,
            noLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '无风险',
            ).length,
        };
        setStats(newStats);
    }, [dataSourceOrigin]);
    const getData = (id) => {
        setLoading(true);
        getReviewAppList(id).then((res) => {
            setDataSource(res);
            setDataSourceOrigin(res);
            setLoading(false);
            //setData([{ name: '111', useVersion: '222' }]);
        });
    };
    
    const saveData = async () => {
        const filterArray = [];
        dataSource.forEach((item) => {
            if (item.isEdit) {
                filterArray.push(item);
            }
        });
        if(filterArray.length === 0){
            message.error('请先进行审阅，再进行保存！')
            return;
        }
        try {
          const result = await axios.put(
            `${BaseUrlEnum.BASELINEAPI}/open-source/compt/baseline/review/app/update`, 
            filterArray, // 直接传递JSON数组
            {
              headers: {
                'Content-Type': 'application/json', // 设置请求头
              },
            }
          );
          message.success('更新成功');
          getData(applicationId);
          setIsEdit(false)
          console.log('服务器响应:', result.data);
        } catch (error) {
         message.error(error)
        }
      };
   
    // 表格列配置
    const columns = [
        {
            title: '应用程序',
            dataIndex: 'applicationName',
            render: (text,record) => {
                return (
                    <div
                        title={text}
                        style={{ color: '#008cd6',cursor:'pointer',width:'150px' }}
                        className='text-overflow' 
                        onClick={() => {
                            // const id=record.applicationSystemId;
                        //   history.push(`/kepler-webpage/svc/integrationService/applicationIntegration/scaAnalysisNew/${record.appId}`,
                        //   { 
                                  
                        //     isFromReview: true
                           
                        //   }
                        //   )
                        history.push(
                            `/kepler-webpage/svc/integrationService/applicationIntegration/scaAnalysisNew/${record.appId}`,
                            { from: '/current-page' });
                        }}
                    >
                        {text}
                    </div>
                );
            }
        },
        {
            title: '应用程序英文名称',
            dataIndex: 'applicationEnName',
        },

        {
            title: '审阅人',
            dataIndex: 'reviewer',
        },
        {
            title: '审阅时间',
            dataIndex: 'reviewTime',
            render: (text) => {
                return (
                    <div
                        className="ellipsis"
                        style={{ width: '150px' }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '审阅意见',
            dataIndex: 'reviewOpinion',
            render: (text, record, index) => {
                return (
                    taskId == 'add'?
                    <Input
                    value={text}
                    maxLength={100}
                    style={{ width: '250px' }}
                    showCount
                    onChange={(e) => {
                        const newData = [...dataSource];
                        newData.forEach((item, key) => {
                            if (item.id === record.id) {
                                newData[key].reviewOpinion = e.target.value;
                                newData[key].isEdit = true;
                            }
                        });
                        setDataSource(newData);
                    }}
                />:(
                    isEdit?  <Input
                    value={text}
                    maxLength={100}
                    style={{ width: '250px' }}
                    showCount
                    onChange={(e) => {
                        const newData = [...dataSource];
                        newData.forEach((item, key) => {
                            if (item.id === record.id) {
                                newData[key].reviewOpinion = e.target.value;
                                newData[key].isEdit = true;
                            }
                        });
                        setDataSource(newData);
                    }}
                />:<div className='text-overflow' style={{width:'250px',maxWidth:'250px'}} title={text}>{text}</div>
                )

                );
            },
        },
        {
            title: '风险等级',
            dataIndex: 'riskLevel',
            render: (text, record, index) => {
                console.log(text);
                return (
                  taskId == 'add'?
                  <Select
                    defaultValue={text}
                    style={{ width: 150 }}
                    onChange={(value) => {
                        const newData = [...dataSource];
                        newData[index].riskLevel = value;
                        newData[index].isEdit = true;
                        setDataSource(newData);
                    }}
                    options={[
                        { value: '高风险', label: '高风险' },
                        { value: '中风险', label: '中风险' },
                        { value: '低风险', label: '低风险' },
                        { value: '无风险', label: '无风险' },
                    ]}
                />
                  :(
                    isEdit? <Select
                    defaultValue={text}
                    style={{ width: 150 }}
                    onChange={(value) => {
                        const newData = [...dataSource];
                        newData[index].riskLevel = value;
                        newData[index].isEdit = true;
                        setDataSource(newData);
                    }}
                    options={[
                        { value: '高风险', label: '高风险' },
                        { value: '中风险', label: '中风险' },
                        { value: '低风险', label: '低风险' },
                        { value: '无风险', label: '无风险' },
                    ]}
                />:text
                  )
                );
            },
        },
    ];

    const onSearch = (value) => {
        setName(value);
    };
    const handleTableChange = (page) => {
        setPagination((prev) => ({
            ...prev,
            current: page.current,
            pageSize: page.pageSize,
        }));
        // getData(page.current, page.pageSize);
        setEditingKey('');
    };

    useEffect(() => {
        // getData(1, 10);
        setPagination({
            ...pagination,
            current: 1,
        });
    }, [name]);
    // const linkList = [
    //     {
    //       title: '新增审阅'
    //     }
    //   ]
      const linkList = [
        {
          href: '/#/openSourceComponent/review',
          title: '开源组件审阅'
        },
        {
         
          title: taskId == 'add'?`新增审阅`:'查看审阅'
        },
      ]
      const filterOption = (input, option) => {
        return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
      };
  return (
    <div className="openSourceComponent ">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} isNeedBack />
      <div  className='micro-spin-container-box sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header" style={{marginBottom:'0px',height:'150px'}}>
          <div className="middleware-base-info">
          <Row>
            <div className='flex-app' style={{marginBottom:'25px'}}>
                <div className='header-icon'></div>
                <div>
                    {
                        taskId === 'add'?'新增审阅':'查看审阅'
                    }
                </div>
            </div>
          </Row>
            <Row>
                <Col span={24} style={{display:'flex',justifyContent:'space-between'}}>
                    <Select
                        style={{ width: '250px' }}
                        showSearch
                        value={applicationId}
                        onChange={(value) => {
                            setApplicationId(value);
                        }}
                        onSearch={onSearch}
                        filterOption={filterOption}
                        disabled={taskId !== 'add'}
                    >
                        {application.map((item) => (
                            <Option key={item.id} value={item.id}>
                                {item.cnName}
                            </Option>
                        ))}
                    </Select>
                </Col>
                
            </Row>
            <div
                style={{
                    marginBottom: '10px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginTop:'20px'
                }}
                >
                    <div>
                        <p style={{ fontSize: '18px', color: '#3D3D3D' }}>
                            总结：共{dataSource.length}个应用程序，其中
                            {stats.highLevel}个高风险，{stats.mediumLevel}
                            个中风险，{stats.lowLevel}个低风险，{stats.noLevel}
                            个无风险
                        </p>
                    </div>
                    <div>
                        <Space>
                        {
                            taskId == 'add'?
                            <Button
                            type="primary"
                            onClick={() => {
                                saveData();
                            }}>
                            保存
                            </Button>:
                            (
                                isEdit?  <Button
                                type="primary"
                                onClick={() => {
                                    saveData();
                                }}
                            >
                                保存
                            </Button>:
                            <Button type="primary" onClick={()=>{setIsEdit(true)}}>
                            编辑
                            </Button>
                            )
                        }
                     
                          
                        </Space>
                    </div>
                </div>
          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section" style={{marginTop:'0px'}}>
            <Table
                size="small"
                bordered={false}
                scroll={{
                    x: 1000, // 设置最小表格宽度，触发横向滚动
                }}
                columns={columns}
                dataSource={dataSource}
                rowKey={(record) => record.id}
                rowClassName="editable-row"
                pagination={{
                    ...pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `共 ${total} 条`,
                    pageSizeOptions: ['5', '10', '15', '20'],
                    defaultCurrent: 1,
                }}
                onChange={handleTableChange}
                loading={loading}
            />
        </div>
      </div>
  
     
    </div>
  );
};



export default AddReviewList;