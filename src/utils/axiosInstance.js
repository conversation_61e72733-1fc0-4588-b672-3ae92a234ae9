import axios from "axios";
import { message } from "antd";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import Local from "@/utils/storage";
import locale from "@/locale";
import errorCode from "./errorCode";
import { localStorageEnum } from "@/enums/storageEnum";
import { history } from "umi";
import { noAuthPage } from "@/constants";
import handleErrorRedirect from "@/utils/handleErrorRedirect";

const TOKEN_ENUM = localStorageEnum.TOKEN 
const USERINFO = localStorageEnum.USERINFO 

if (!window.Promise) {
  window.Promise = Promise;
}

NProgress.configure({
  minimum: 0.1,
  easing: "ease",
  speed: 800,
  showSpinner: false,
});

axios.interceptors.request.use(
  (config) => {
    NProgress.start();
    config.params = config.params || {};
    config.headers = {
      Authorization: `Bearer ${Local.getLocal(TOKEN_ENUM)}`,
      language: Local.getLocal("language"),
      ...config.headers,
    };
    
    const localUser = Local.getLocal(USERINFO);
    const tenantCode = localUser?.tenantCode;
    const appCode = localUser?.appCode;
    if (config.headers?.tenantCode) {
        delete config.headers.tenantCode
    }
    config.headers['Tenant'] = tenantCode??null;
    config.headers['App'] = appCode??null;
    return config;
  },
  (err) => {
    handleErrorRedirect.onNoNetwork()
    NProgress.done();
    return Promise.reject(err);
  }
);

// http response 拦截器
axios.interceptors.response.use(
  (response) => {
    NProgress.done();
    if (response.data.errorMsg) {
      console.error("response error: ", response.data.errorMsg);
      return Promise.reject(response);
    } else {
      return response;
    }
  },
  (err) => {
    NProgress.done();
    if (err.response.status === 403 || err.response.status === 500 || err.response.status === 502 || err.response.status === 504 || err.response.status === 404) {
      // handleErrorRedirect.onNoNetwork()
      console.error("response error: ", err.response)
    }
    if (err && err.response && err.response.status === 401) {
      if(noAuthPage.includes(history.location.pathname)) return
      const unauthorizedEvent = new CustomEvent('unauthorized');
      window.dispatchEvent(unauthorizedEvent);
      const redirectLoginPage = err.response.headers["redirect-login-page"];
      if(redirectLoginPage){
        const redirectUrl = redirectLoginPage.indexOf("?")>-1 ? `${redirectLoginPage}&redirect=${window.location.href}` : `${redirectLoginPage}?redirect=${window.location.href}`
        window.location.href = redirectUrl
      }
    }

    return Promise.reject(err);
  }
);

/**
 * _get方法，对应get请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 * @param {Object} option [请求配置]
 * @param {String} method [请求方法]
 */
function _get(
  url,
  { params = {}, option = {}, method = "GET", headers = {} } = {}
) {
  return new Promise((resolve, reject) => {
    const { restUrl, data } = restfulAPI(url, params, method);
    let options = {
      url: restUrl,
      params: data,
      method,
      headers,
      ...option,
    };
    axios(options)
      .then((res) => {
        if (res.data.code !== 0) {
          res.data.message && message.warning(res.data.message);
          reject(res.data);
        } else {
          resolve(res.data.data);
        }
      })
      .catch((err) => {
        reject(err.data);
      });
  });
}

function _delete(url, params = {}, option = {}) {
  return _get(url, {params, option, method: "DELETE"});
}

/**
 * _post方法，对应post请求
 * @param {String} url [请求的url地址]
 * @param {Object} params [请求时携带的参数]
 * @param {Object} option [请求配置]
 * @param {String} method [请求方法]
 */
function _post(
  url,
  { params = {}, option = {}, headers = {}, method = "POST" }
) {
  return new Promise((resolve, reject) => {
    const { restUrl, data } = restfulAPI(url, params);
    let options = {
      url: restUrl,
      data: data,
      method,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
        ...headers,
      },
      ...option,
    };
    axios(options)
      .then((res) => {
        if (res.data.code !== 0) {
          res.data.message && message.warning(res.data.message) ;
          reject(res.data);
        } else {
          resolve(res.data.data);
        }
      })
      .catch(({response}) => {
        reject(response?.data?.message || response?.data?.msg);
      });
  });
}
function _put(
  url,
  { params = {}, option = {}, headers = {}, method = "PUT" }
) {
  return new Promise((resolve, reject) => {
    const { restUrl, data } = restfulAPI(url, params);
    let options = {
      url: restUrl,
      data: data,
      method,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
        ...headers,
      },
      ...option,
    };
    axios(options)
      .then((res) => {
        if (res.data.code !== 0) {
          res.data.message && message.warning(res.data.message) ;
          reject(res.data);
        } else {
          resolve(res.data.data);
        }
      })
      .catch(({response}) => {
        reject(response?.data?.message || response?.data?.msg);
      });
  });
}

// function _put(url, { params = {}, option = {} }) {
//   return _post(url, params, option, "PUT");
// }

const restfulAPI = function (url, formData) {
  const newFormData = Array.isArray(formData) ? [...formData] : { ...formData };
  if (!url) throw new Error(locale.notice.url_not_empty);
  if (url.indexOf("{") === -1 || !formData)
    return { restUrl: url, data: newFormData };
  let restfulArray = url.split("/");
  const result = restfulArray.map((item) => {
    if (item.indexOf("{") !== -1) {
      const key = item.substring(1, item.length - 1);
      delete newFormData[key];
      return formData[key] || "";
    }
    return item;
  });
  return { restUrl: result.join("/"), data: newFormData };
};
function _postF(
  url,
  { params = {}, option = {}, headers = {}, method = "POST" }
) {
  return new Promise((resolve, reject) => {
    const { restUrl, data } = restfulAPI(url, params);
    let options = {
      url: restUrl,
      data: data,
      method,
      ...option,
    };
    axios(options)
      .then((res) => {
        if (res.data.code !== 0) {
          res.data.message && message.warning(res.data.message) ;
          reject(res.data);
        } else {
          resolve(res.data.data);
        }
      })
      .catch(({response}) => {
        reject(response?.data?.message || response?.data?.msg);
      });
  });
}

export default {
  get: _get,
  delete: _delete,
  post: _post,
  put: _put,
  restfulAPI: restfulAPI,
  postF:_postF
};
