import request from "@/utils/axiosInstance";
import { API_AMP } from '@/services';

// 获取用户未读消息
export async function fetchNotices(params) {
  const { userId, ...rest } = params;
  return request.get(`${API_AMP}/messages/pull/${userId}`, { params: rest });
}

// 获取用户未读消息数量
export async function fetchNoticeList(params) {
  const { userId, ...rest } = params;
  return request.get(`${API_AMP}/inbox/${userId}`, { params: rest });
}

// 修改消息状态
export async function putStatus(id, params) {
  return request.post(`${API_AMP}/inbox/put/${id}`, {
    params,
    headers: { 'Amp-Organ-Id': params.tenantId },
  });
}
