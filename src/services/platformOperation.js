import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'
// 脚手架使用情况
export const scaffoldTemplate = (params) => {
    return request.get(`${BaseUrlEnum.BASELINEAPI}/base/dashboard/scaffold-template`,{params})
  };
 // 技术栈使用情况
export const techStackRank = (params) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/base/dashboard/tech-stack-rank`,{params})
}; 
export const docCount = (params) => {
  return request.get(`${BaseUrlEnum.PLATDOC}/provider/document/count`,{params})
};
export const apiCount = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/api/count`,{params})
};
export const statisticApp = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/apps/statistic/app`,{params})
};
export const statisticMiddle = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/apps/statistic/middle`,{params})
};
export const getUserList = (params) => {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/u/users`,{params})
};
export const getUserSysCommit = (params) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/base/dashboard/user-sys-commit`,{params})
};




  
  