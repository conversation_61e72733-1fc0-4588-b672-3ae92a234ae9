import request from "@/utils/axiosInstance";
export const CONFIG_MIDDLEWARE = {
  clusterId: 'default--test',
  ...window._th_appInfo?._middleware,
};
export const Kepler_Base = '/kepler/integration/product'

export const API_MIDDLEWARE = (CONFIG_MIDDLEWARE._prefixApi || "") + "/apimiddleware";
// http://10.10.102.52:31088/api/middlewares/info/groups?noCache=1744685282707&clusterId=default--test&filterUninstalled=false&keyword=

const usertoken= "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ1c2VySW5mbyIsInVzZXJJbmZvIjoie1wiYWxpYXNOYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcImlzQWRtaW5cIjp0cnVlLFwidXNlcm5hbWVcIjpcImFkbWluXCJ9IiwiZXhwIjoxNzQ1MjI5NDc5LCJpYXQiOjE3NDUyMjU1Nzl9.gJMsKWzwDId19IcDqMa3Vzg9_Zs50Eyitbe5dE4zIm4"
// 获取中间件应用列表
export const getMiddlewareAppList = (params,headers) => {
  return request.get(`${API_MIDDLEWARE}/middlewares/info/groups`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};


// 获取中间件应用列表
export const getMiddlewareVersion = (params,headers) => {
  return request.get(`${API_MIDDLEWARE}/middlewares/info/version`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};

// 获取中间件应用信息
export const getMiddlewareMeta = (params,headers) => {
  return request.get(`${API_MIDDLEWARE}/middlewares/info/chartMetadata`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};


// 获取中间件应用文件列表
export const getMiddlewareFileList = (params,headers) => {
  return request.get(`${API_MIDDLEWARE}/middlewares/info/chartNames`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};

// 获取中间件应用文件内容
export const getMiddlewareFileContent = (params,headers) => {
  return request.get(`${API_MIDDLEWARE}/middlewares/info/fileContent`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};

// 获取中间件应用参数列表
export const getMiddlewareHideParams = (params,headers) => {
  return request.get(`${API_MIDDLEWARE}/middlewares/${params.type}/hideParams`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};

// 获取中间件应用参数列表
export const getMiddlewareOperatorStatuss = (params,headers) => {
  return request.get(`${API_MIDDLEWARE}/middlewares/info/operatorStatus`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};
// 获取中间件应用列表
export const getAllMiddlewareAppList = (params,headers) => {
  return request.get(`${Kepler_Base}/list`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};
// 收藏产品
export const addFavorite = (params,headers) => {
  return request.post(`${Kepler_Base}/add-favorite/${params.id}`,{
    params: { ...params, noCache: new Date().getTime() },
    headers: {...headers, usertoken: usertoken},
  });
};
// 取消收藏产品
export const removeFavorite = (params,headers) => {
  return request.delete(`${Kepler_Base}/remove-favorite/${params.id}`)
};

