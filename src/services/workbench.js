import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'

export const getWorkbenchInfo = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/userInfo`)
};

export const getWorkbenchData = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/info`)
};

export const getWorkbenchConfig = () => {
    return request.get(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/getConfig/1`)
}

export const saveWorkbenchConfig = (params) => {
    return request.post(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/saveConfig`, {params})
}

export const getNoticeList = (params) => {
    return request.post(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/notice`, {params})
}

