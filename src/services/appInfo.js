import request from "@/utils/axiosInstance";
import { API_AMP } from "@/services";
import { BaseUrlEnum } from '@/enums/httpEnum'


// 通过当前应用链接获取应用信息
export const getAppInfoByUrl = (params) => {
  return request.get(`${BaseUrlEnum.KEPLER}/expose/info/appId/url`,{params});
};


/**
 * 获取应用自定义头部
 * @param params 应用id
 */
export function getHeaderBar(params) {
  return request.get(`${BaseUrlEnum.KEPLER}/aims/apps/headBar/${params}`);
}


/**
 * 切换租户
 */
export const changeTenant = params => {
  return request.post(`${BaseUrlEnum.KEPLER}/upms/u/users/change/tenant/${params}`,{});
};


/**
 * 获取租户tree
 */
export function getTenantTree(params) {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/u/tenants/tree`,params);
}


/**
 * 获取应用菜单
 * @param params 应用id
 */
export function getAppMenu(params) {
  // return request.get(`${BaseUrlEnum.KEPLER}/expose/menu/appId/${params}`,{});
  return request.get(`${BaseUrlEnum.KEPLER}/upms/p/reso/cloud/permissions/current/user`);
}

/**
 * 获取微应用列表
 * @param params 应用id
 */
export function getMicroApps(params) {
  return request.get(`${BaseUrlEnum.KEPLER}/aims/micro/apps`, {params});
}