import { useState } from "react";
import { getAppMenu } from "@/services";
import _ from "lodash";
import { formatMenuData } from "@/utils/menus";
import { history } from "umi";

export default function menuInfo() {
  // 菜单总的数据
  const [totalMenus, setTotalMenus] = useState([]);
  // 顶部菜单数据
  const [topMenus, setTopMenus] = useState([]);
  // 当前激活的顶部菜单
  const [activeTopMenu, setActiveTopMenu] = useState(null);
  // topMenuPathMap 映射表，将路径前缀与顶部菜单 Key 关联：
  const [topMenuPathMap, setTopMenuPathMap] = useState(null);
  // 当前激侧栏菜单数据
  const [sidebarMenus, setSidebarMenus] = useState([]);
  // 当前侧栏菜单的选中
  const [selectedKeys, setSelectedKeys] = useState([]);
  // 当前侧栏菜单的展开
  const [openKeys, setOpenKeys] = useState([]);
  // 当前侧栏菜单展开还是收起
  const [sideBarExpand, setSideBarExpand] = useState([]);

  // 当前访问路劲，保证字组件和父组件的location.pathName一致
  const [currentPath, setCurrentPath] = useState("/");

  // 会默认隐藏sideBar的菜单，默认隐藏二级菜单，通过点击一级菜单渲染页面
  const hiddenSideBarMenus = [
    "/kepler-webpage/svc/integrationService/applicationIntegration",
  ];

  // 用backLayout布局的页面的菜单的静态菜单，即不需要查询接口返回的菜单
  const homeLayoutStaticMenus = new Map([// 天合
    [window._th_appInfo._appId, [{
        id: `${window._th_appInfo._appId}-homePage`,
        name: "首页",
        path: "/homePage"
      },{
        id: `${window._th_appInfo._appId}-dochub`,
        name: "技术文档库",
        path: "/dochub/docs"
      },{
        id: `${window._th_appInfo._appId}-productService`,
        name: "技术组件和服务",
        path: "/productService"
      }
      // ,{
      //   id: `${window._th_appInfo._appId}-workbench`,
      //   name: "我的工作台",
      //   path: "/myWorkbench"
      // }
    ]]
  ]);

  // 获取不需要权限菜单的集合
  const getStaticMenus = (appId) => {
    return formatMenuData(homeLayoutStaticMenus.get(appId) ?? []);
  };
  // 获取不需要权限菜单的集合path
  const getStaticMenusPath = (appId) => {
    return homeLayoutStaticMenus.get(appId)?.map((item) => item.path) ?? [];
  };

  // 获取异步菜单数据
  const getAsyncMenus = async () => {
    try {
      const menusData = await getAppMenu()
      let menus = menusData ?? [];
      // TODO: 海创园先死菜单数据以便调试
     if(window.isHcy){
        menus = [{
            id: "12121",
            name: "应用系统管理",
            isIframe:0,
            path: "/kepler-webpage/svc/integrationService/applicationIntegration",
            children:[{
              id:"1232323333326464712121",
              parentId: "12121",
              name: "基础信息",
              isIframe:0,
              path: "/kepler-webpage/svc/integrationService/applicationIntegration/detail/:id/inner",
            },
            {
              id:"1232323333326464712122",
              parentId: "12121",
              name: "应用程序",
              isIframe:0,
              path: "/kepler-webpage/svc/integrationService/applicationIntegration/app/:id",
            },
            {
              id:"1232323333326464712123",
              parentId: "12121",
              name: "系统配置",
              isIframe:0,
              path: "/kepler-webpage/svc/integrationService/applicationIntegration/config/:id",
            },
            {
              id:"1232323333326464712124",
              parentId: "12121",
              name: "应用设置",
              isIframe:0,
              path: "/kepler-webpage/svc/integrationService/applicationIntegration/appSetup/:id",
            },
            // {
            //   id:"1232323333326464712123",
            //   parentId: "12121",
            //   name: "创建应用程序",
            //   isIframe:0,
            //   path: "/kepler-webpage/svc/integrationService/applicationIntegration/createApp",
            // },
            // {
            //   id:"1232323333326464712124",
            //   parentId: "12121",
            //   name: "模版创建应用程序",
            //   isIframe:0,
            //   path: "/kepler-webpage/svc/integrationService/applicationIntegration/createAppbyTempalte",
            // }
          ]
          },{
            id: "123232333332",
            name: "应用程序管理",
            path: "/system/systemManage/parent-management",
            children:[{
              id:"12323233333264647",
              parentId: "123232333332",
              name: "基础信息",
              isIframe:0,
              path: "/system/systemManage/:systemId/baseInfo",
            },{
              id:"12323233333264648",
              parentId: "123232333332",
              name: "流水线",
              isIframe:0,
              path: "/system/systemManage/:systemId/pipelineManage",
            },{
              id:"22323233333264648",
              parentId: "123232333332",
              name: "代码仓库",
              isIframe:0,
              path: "/efficiency/collaborationCodeManage/:systemId/detail",
            },{
              id:"123232333332646481",
              parentId: "123232333332",
              name: "开源组件登记",
              isIframe:0,
              path: "/system/systemManage/:systemId/opensourceComponent",
            },
            {
              id:"12323233333264649",
              parentId: "123232333332",
              name: "代码扫描",
              isIframe:0,
              path: "/system/systemManage/:systemId/codeManage",
            },{
              id:"12323233333264749",
              parentId: "123232333332",
              name: "运行实例",
              isIframe:0,
              path: "/system/systemManage/:systemId/runNode",
            },{
              id:"1232323333326484945",
              parentId: "123232333332",
              name: "服务设置",
              isIframe:0,
              path: "/system/systemManage/:systemId/setting",
            }]
          },{
            id: "45848578979",
            name: "平台配置",
            path: "",
            children:[{
              id:"12323233333264647558789789",
              parentId: "45848578979",
              name: "用户管理",
              isIframe:0,
              path: "/kepler-webpage/svc/customService/userMgmt"
            },{
              id:"12323233333264647558789798",
              parentId: "45848578979",
              name: "组织部门管理",
              isIframe:0,
              path: "/kepler-webpage/svc/customService/orgMgmt",
            },{
              id:"12323233333264647558789791",
              parentId: "45848578979",
              name: "角色管理",
              isIframe:0,
              path: "/kepler-webpage/svc/permissionService/roleMgmt",
            },{
              id:"12323233333264647558789792",
              parentId: "45848578979",
              name: "权限管理",
              isIframe:0,
              path: "/kepler-webpage/svc/permissionService/permsMgmt",
            },{
              id:"12323233333264647558789793",
              parentId: "45848578979",
              name: "菜单管理",
              isIframe:0,
              path: "/kepler-webpage/svc/permissionService/appMenuMgmt",
            },{
              id:"12323233333264647558789794",
              parentId: "45848578979",
              name: "标签管理",
              isIframe:0,
              path: "/kepler-webpage/svc/permissionService/labelMgmt",
            },{
              id:"12323233333264647558789795",
              parentId: "45848578979",
              name: "租户管理",
              isIframe:0,
              path: "/kepler-webpage/svc/customService/tenantMgmt",
            }
          ]
          },{
            id: "45848578979787878",
            name: "审批中心",
            path: "/kepler-webpage/svc/procService/procCenter",
            children:[{
              id:"45848578979787878789",
              parentId: "45848578979787878",
              name: "我的审批",
              isIframe:0,
              path: "/kepler-webpage/svc/procService/verproc"
            },{
              id:"4584857897978787858789798",
              parentId: "45848578979787878",
              name: "我的提交",
              isIframe:0,
              path: "/kepler-webpage/svc/procService/procMgmt",
            }]
          },
          {
            id:"458485789797878710",
            name: "开源组件管理",
            path: "/openSourceComponent"
          }
        ]
      }
      setTotalMenus(formatMenuData(menus))
    } catch (error) {
      console.log(error);
    }
  };

  const jumpAndUpdateSideBarMenus = (path, params, isReplace = false) => {
    if (!path) {
      return "请传入有效的path";
    }
    window._MiCROAPP_JUMP_INFO_.set(path, params);
    isReplace ? history.replace(path) : history.push(path);
  };

  return {
    totalMenus,
    topMenus,
    activeTopMenu,
    sidebarMenus,
    sideBarExpand,
    hiddenSideBarMenus,
    topMenuPathMap,
    selectedKeys,
    openKeys,
    currentPath,
    setActiveTopMenu,
    setTopMenus,
    setTopMenuPathMap,
    setSidebarMenus,
    setSideBarExpand,
    getStaticMenus,
    getStaticMenusPath,
    getAsyncMenus,
    setSelectedKeys,
    setOpenKeys,
    jumpAndUpdateSideBarMenus,
    setCurrentPath,
  };
}
