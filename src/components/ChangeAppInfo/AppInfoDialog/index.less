.subMenuList {
    position: absolute;
    top: calc(@header-height + 8px);
    left: calc(@sidebar-width + 8px);
    background: #fff;
    min-height: 240px;
    max-height: 360px;
    min-width: 250px;
    box-sizing: border-box;
    font-size: 12px;
    z-index: 99;
    box-shadow: 0 1px 10px 0px rgb(0 0 0 / 8%);
    padding: 6px;
    display: flex;
    justify-content: space-between;
    .appSystemContent,.appProgramContent{
      width: 250px;
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    .subMenuListTitle{
      width: 100%;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: @text-primary-color;
      padding: 0 10px;
    }
    .subMenuListItemContent {
      padding-top: 4px;
      flex: 1;
      overflow: hidden;
    }
    .subMenuListSearch {
      width: 100%;
      height: 33px;
      display: flex;
    }
    .subMenuListItem {
      width: 100% ;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      .appItem {
        cursor: pointer;
        height: 40px;
        line-height: 40px;
        color: @text-secondary-color;
        padding-left: 16px;
        border-radius: 4px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .appLogo{
          width: 20px;
          height: 20px;
          line-height: 20px;
          color: #fff;
          margin-right: 6px;
          border-radius: @border-radius-normal;
          background-color: #ffbc03;
          text-align: center;
          overflow: hidden;
        }
        .appName {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &:hover {
          color: @primary-color;
          background-color: #E0EAFF;
        }
      }
      .appItemSelected {
        color: @primary-color;
        background-color: #E0EAFF;
      }
    }
    &.hide {
      max-width: 0px;
      display: none;
    }
  }
  