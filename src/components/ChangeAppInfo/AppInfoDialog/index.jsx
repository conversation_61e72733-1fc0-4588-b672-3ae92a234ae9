import { useEffect, useState } from "react";
import { useLocation, useModel } from "umi";
import { getAppSystemListApi, getAppProgramListApi, getAppProgramDetailApi } from "@/services/appSystem";
import { Spin, Empty } from 'antd';
import styles from "./index.less";
import { findTopMenuKey, findRowPathByPath, flattenMenuData, generateSidebarMenuByParams } from "@/utils/menus";
import storage from "@/utils/storage";
import { sessionStorageEnum } from '@/enums/storageEnum';

const AppInfoDialog = (props) => {
  const location = useLocation();
  const { 
    selectAppInfo, 
    setSelectAppInfo, 
    className,
    visible,
    setVisible,
    isProgram,
    setIsProgram,
    setCurrentAppSystemAndProgramDetail,
    isLinkChange,
    setIsLinkChange,
    setCardLoading
  } = props;
  // 应用系统列表
  const [appSystemList, setAppSystemList] = useState([]);
  // 应用程序列表
  const [appProgramList, setAppProgramList] = useState([]);
  const [ appSystemLoading, setAppSystemLoading ] = useState(false);
  const [ appProgramLoading, setAppProgramLoading] = useState(false);
  const [ currentAppSystem, setCurrentAppSystem ] = useState(selectAppInfo);
  const { jumpAndUpdateSideBarMenus, topMenuPathMap, topMenus, setSidebarMenus } = useModel('menu');
  
  useEffect(() => {// 当跳转了应用系统、应用程序的相关页面时，只携带了id数据，需要根据id获取name以展示
    if(!isLinkChange) return
    if(selectAppInfo.appProgramId){// 当前是应用应用程序界面
      setIsProgram(true)
      if(!selectAppInfo.appSystemId){// 通过应用程序id获取应用系统id信息
        getAppProgramDetail(selectAppInfo.appProgramId)
      }
      if(selectAppInfo.appSystemId && !appProgramList.length){// 应用程序列表为空时，并且应用系统id存在时，自动获取应用程序列表
        getAppProgramList(selectAppInfo.appSystemId)
      }
      if(appSystemList.length && appProgramList.length && selectAppInfo.appSystemId && selectAppInfo.appProgramId){
        const tempAppSystem = appSystemList.find(item => item.id === selectAppInfo.appSystemId)
        const tempAppProgram = appProgramList.find(item => item.id === selectAppInfo.appProgramId)
        if(!tempAppProgram){// 在应用程序列表中没有找到应用程序信息时，可能是新创建了应用程序，但没有调用getAppProgramList更新应用程序列表信息，所以在列表找不到新创建的应用程序信息时，需要重新获取应用程序列表信息
          getAppProgramList(selectAppInfo.appSystemId)
        }
        if(tempAppSystem && tempAppProgram){
          setSelectAppInfo({
            ...selectAppInfo,
            appSystemId: tempAppSystem.id,
            appSystemName: tempAppSystem.cnName,
            appProgramName: tempAppProgram.programNameCn
          })
          setCurrentAppSystemAndProgramDetail({
            appSystemDetail: tempAppSystem,
            appProgramDetail: tempAppProgram
          })
        }
      }
    }else{// 当前是应用应用系统界面
      setIsProgram(false)
      if(!selectAppInfo.appSystemName && appSystemList.length){
        const tempAppSystem = appSystemList.find(item => item.id === selectAppInfo.appSystemId)
        if(tempAppSystem){
          setSelectAppInfo({
            ...selectAppInfo, 
            appSystemName: tempAppSystem.cnName
          })
          setCurrentAppSystemAndProgramDetail({
            appSystemDetail: tempAppSystem,
            appProgramDetail: null
          })
        }
      }
    }
  },[isLinkChange, selectAppInfo, appSystemList, appProgramList])

  useEffect(() => {
    getAppSystemList();
  },[])

  // 当弹窗被打开时，给currentAppSystem赋值
  useEffect(() => {
    if(!visible) return
    setCurrentAppSystem({
      ...selectAppInfo
    });
  },[visible])

  const getAppSystemList = async() => {
    try {
      setAppSystemLoading(true)
      setCardLoading(true)
      const data = await getAppSystemListApi();
      setAppSystemLoading(false)
      setCardLoading(false)
      if (data) {
        setAppSystemList(data ?? []);
      }
    }catch (error) {
      console.log(error);
    }
  };

  const getAppProgramList = async(id) => {
    try {
      setAppProgramLoading(true)
      const data = await getAppProgramListApi({applicationId:id,pageNo:1,pageSize:10000});
      setAppProgramLoading(false)
      if (data) {
        setAppProgramList(data?.records ?? []);
      }
    }catch (error) {
      console.log(error);
    }
  };

  const getAppProgramDetail = async(id) => {
    try {
      setCardLoading(true)
      const data = await getAppProgramDetailApi(id);
      setCardLoading(false)
      if (data) {
        if(data?.applicationId){
          setSelectAppInfo({
            ...selectAppInfo,
            appSystemId: data.applicationId,
          })
        }
      }
    }catch (error) {
      console.log(error);
    }
  };


  // 切换应用系统或应用程序需更新侧栏菜单并访问新的携带参数的url
  const changeAppSystemEffect = (selectAppInfoTemp) => {
    const params = {
      id: selectAppInfoTemp.appSystemId,
      systemId: selectAppInfoTemp.appProgramId
    }
    const pathName = location.pathname;
    const currentTopKey = findTopMenuKey(pathName, topMenuPathMap);
    let rowPath = "";
    if(currentTopKey){// 如果当前正处于菜单数据界面
      const curTopMenu = topMenus?.find(item => item.id === currentTopKey)
      const { pathMap } = flattenMenuData(curTopMenu?.children ?? [])
      rowPath = findRowPathByPath(pathName, pathMap)
    }else{// 如果当前正处于非菜单数据界面，比如内页
      const currentMenuStateCollection = storage.getSession(sessionStorageEnum.currentMenuStateCollection)
      currentMenuStateCollection.routeParamsInfoCache = {
        ...currentMenuStateCollection.routeParamsInfoCache,
        params
      }
      if(currentMenuStateCollection?.sidebarMenus?.length > 0){
        currentMenuStateCollection.sidebarMenus = generateSidebarMenuByParams(currentMenuStateCollection.sidebarMenus, params)
        // 更新本地存储信息，子应用监听到currentAppSystemAndProgramDetail变化再触发跳转逻辑会重组sidebarMenus，案例：当处于应用系统-系统设置-分配开发人员详情页时切换应用系统
        storage.setSession(sessionStorageEnum.currentMenuStateCollection, currentMenuStateCollection)
        setSidebarMenus(currentMenuStateCollection.sidebarMenus)
      } 
    }
    jumpAndUpdateSideBarMenus(rowPath, params, true);
  }
  // const handleSearch = (e) => {
  //   const value = e.target.value;
  //   console.log(value)
  // };

  const onChange = async (data, handleProgram) => {
    setIsLinkChange(false)
    if(handleProgram){// 处理应用程序面板点击事件
      const selectAppInfoTemp = {
        appSystemId: currentAppSystem.appSystemId,
        appSystemName: currentAppSystem.appSystemName,
        appProgramId: data.id,
        appProgramName: data.programNameCn
      };
      setSelectAppInfo(selectAppInfoTemp);
      setCurrentAppSystem((prev) => {
        return {
          ...prev,
          ...selectAppInfoTemp
        }
      });
      setCurrentAppSystemAndProgramDetail((prev) => {
        return {
          ...prev,
          appProgramDetail: data
        }
      })
      setVisible(false);
      changeAppSystemEffect(selectAppInfoTemp)
      return
    }

    /** 处理应用系统面板点击事件 */ 
    if(!isProgram) {// 当前是应用应系统界面，选择了应用系统数据，直接设置值
      const selectAppInfoTemp = {
        appSystemId: data.id,
        appSystemName: data.cnName,
        appProgramId: null,
        appProgramName: null
      }
      setSelectAppInfo(selectAppInfoTemp);
      setCurrentAppSystemAndProgramDetail({
        appSystemDetail: data,
        appProgramDetail: null
      })
      setVisible(false);
      changeAppSystemEffect(selectAppInfoTemp)
      return
    }
    /** 当前是应用程序界面，需设置临时选中的应用系统数据currentAppSystem，当选完了应用程序后，再设置selectAppInfo */ 
    setCurrentAppSystem({
      appSystemId: data.id,
      appSystemName: data.cnName,
      appProgramId: null,
      appProgramName: null
    });
    // 根据应用系统id获取应用程序
    getAppProgramList(data.id)
  };

  return (
    <div
      className={`${styles.subMenuList} ${className || ""} ${
        visible ? "" : styles.hide
      }`}
      onMouseLeave={() => setVisible(false)}
    >
      <div className={styles.appSystemContent}>
        <div className={styles.subMenuListTitle}>
          应用系统名称
        </div>
        {/* <div className={styles.subMenuListSearch}>
          <Input
            size="small"
            variant="filled"
            placeholder="根据名称搜索"
            onChange={handleSearch}
          />
        </div> */}
        <div
          className={`${styles.subMenuListItemContent}`}
        >
          <Spin style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center'}} spinning={appSystemLoading}></Spin>
          <div
            className={`${styles.subMenuListItem} ${className || ""}`}
          >
            {appSystemList.length === 0 && !appSystemLoading && <Empty description="暂无数据" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}/>}
            {appSystemList.map((item) => (
              <div
                className={`${styles.appItem} ${
                    isProgram ? currentAppSystem.appSystemId === item.id && styles.appItemSelected : (selectAppInfo.appSystemId === item.id && styles.appItemSelected)
                }`}
                key={item.id}
                onClick={() => onChange(item)}
              >
                <img src={item.logoUrl} alt="" className={styles.appLogo} />
                <span className={styles.appName}>{item.cnName}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      {
        isProgram &&
        <div className={styles.appProgramContent}>
          <div className={styles.subMenuListTitle}>
            应用程序名称
          </div>
            {/* <div className={styles.subMenuListSearch}>
              <Input
                size="small"
                variant="filled"
                placeholder="根据名称搜索"
                onChange={handleSearch}
              />
            </div> */}
          <div
            className={`${styles.subMenuListItemContent}`}
          >
            <Spin style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center'}} spinning={appProgramLoading} />
            <div
              className={`${styles.subMenuListItem} ${className || ""}`}
            >
              {appProgramList.length === 0 && !appProgramLoading && <Empty description="暂无数据" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}/>}
              {appProgramList.map((item) => (
                <div
                  className={`${styles.appItem} ${
                    currentAppSystem.appProgramId === item.id
                      ? styles.appItemSelected
                      : ""
                  }`}
                  key={item.id}
                  onClick={() => onChange(item, true)}
                >
                  <div className={styles.appLogo} >{item.programNameCn.split("-")?.[0] || ""}</div>
                  <span className={styles.appName}>{item.programNameCn}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      }
    </div>
  );
};

export default AppInfoDialog;
